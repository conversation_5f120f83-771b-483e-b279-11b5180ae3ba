#!/usr/bin/env python3
"""
Test script to demonstrate the complete AI Visibility analysis flow:
1. Get Firebase token
2. Start analysis
3. Poll for progress
4. View completed reports
"""

import requests
import json
import time
import sys

# Configuration
FIREBASE_API_KEY = "AIzaSyDxBNmjuoMjkS5u8iad6PSB_5Lm7ggIkfY"
API_BASE_URL = "http://127.0.0.1:8000"  # Update if different
EMAIL = "<EMAIL>"
PASSWORD = "Gmk58@usa"

def get_firebase_token():
    """Get Firebase authentication token"""
    print("🔐 Getting Firebase token...")
    
    url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={FIREBASE_API_KEY}"
    payload = {
        "email": EMAIL,
        "password": PASSWORD,
        "returnSecureToken": True
    }
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        data = response.json()
        token = data.get("idToken")
        print(f"✅ Token obtained: {token[:20]}...")
        return token
    else:
        print(f"❌ Failed to get token: {response.status_code} - {response.text}")
        return None

def start_analysis(token):
    """Start AI Visibility analysis"""
    print("\n🚀 Starting AI Visibility analysis...")
    
    url = f"{API_BASE_URL}/ai-visibility/geo/start-analysis"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "brand_info": {
            "brand_name": "Ramp",
            "website_url": "https://ramp.com",
            "description": "Ramp is a U.S.-based corporate card and expense management platform that helps businesses save time and money through automation, real-time spend tracking, and integrated finance tools.",
            "competitor_urls": [
                "https://www.expensify.com",
                "https://www.brex.com",
                "https://www.divvy.com"
            ],
            "sitemap_url": "https://ramp.com/sitemap.xml",
            "robots_url": "https://ramp.com/robots.txt",
            "region": "United States"
        },
        "prompts": [
            {
                "id": "2b034fcb-d5c9-4f70-9245-601aaf75ffa9",
                "text": "ramp vs expensify which is better",
                "category": "comparison",
                "isCustom": False
            },
            {
                "id": "caa29114-1b93-42df-aa74-75781db3076e",
                "text": "corporate card expense management software",
                "category": "recommendation",
                "isCustom": False
            },
            {
                "id": "9e9a803a-238c-4448-8204-e19ece8008d7",
                "text": "ramp business credit card pricing",
                "category": "pricing",
                "isCustom": False
            }
        ],
        "selected_models": ["gpt-4o"]
    }
    
    response = requests.post(url, headers=headers, json=payload)
    if response.status_code == 200:
        data = response.json()
        analysis_id = data.get("analysis_id")
        print(f"✅ Analysis started successfully!")
        print(f"📊 Analysis ID: {analysis_id}")
        print(f"⏱️  Estimated duration: {data.get('estimated_duration_minutes', 'Unknown')} minutes")
        print(f"🔢 Total model calls: {data.get('total_model_calls', 'Unknown')}")
        return analysis_id
    else:
        print(f"❌ Failed to start analysis: {response.status_code} - {response.text}")
        return None

def check_analysis_status(token, analysis_id):
    """Check the status of an analysis"""
    url = f"{API_BASE_URL}/ai-visibility/analysis/{analysis_id}/status"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Failed to check status: {response.status_code} - {response.text}")
        return None

def poll_for_completion(token, analysis_id, max_wait_minutes=10):
    """Poll for analysis completion"""
    print(f"\n⏳ Polling for completion (max {max_wait_minutes} minutes)...")
    
    start_time = time.time()
    max_wait_seconds = max_wait_minutes * 60
    
    while time.time() - start_time < max_wait_seconds:
        status_data = check_analysis_status(token, analysis_id)
        if not status_data:
            break
            
        status = status_data.get("status")
        progress = status_data.get("progress", {})
        overall_progress = progress.get("overall_progress", 0)
        
        print(f"📊 Status: {status} | Progress: {overall_progress:.1f}%")
        
        if status == "completed":
            print("✅ Analysis completed!")
            return True
        elif status == "failed":
            error = status_data.get("error", "Unknown error")
            print(f"❌ Analysis failed: {error}")
            return False
        
        time.sleep(10)  # Wait 10 seconds before next check
    
    print("⏰ Timeout reached")
    return False

def get_reports(token):
    """Get all AI Visibility reports"""
    print("\n📋 Fetching AI Visibility reports...")
    
    url = f"{API_BASE_URL}/ai-visibility/reports"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        data = response.json()
        reports = data.get("reports", [])
        total_count = data.get("total_count", 0)
        
        print(f"✅ Found {total_count} reports")
        
        for i, report in enumerate(reports, 1):
            print(f"\n📊 Report {i}:")
            print(f"   ID: {report.get('id')}")
            print(f"   Brand: {report.get('brand_name')}")
            print(f"   Overall Score: {report.get('overall_score')}%")
            print(f"   Platform Coverage: {report.get('ai_platform_coverage')}%")
            print(f"   Mention Frequency: {report.get('brand_mention_frequency')}%")
            print(f"   Sentiment: {report.get('sentiment_analysis')}%")
            print(f"   Queries Tested: {report.get('total_queries_tested')}")
            print(f"   Platforms: {', '.join(report.get('analyzed_platforms', []))}")
            print(f"   Created: {report.get('created_at')}")
        
        return reports
    else:
        print(f"❌ Failed to get reports: {response.status_code} - {response.text}")
        return []

def main():
    """Main test flow"""
    print("🧪 AI Visibility Analysis Test Flow")
    print("=" * 50)
    
    # Step 1: Get authentication token
    token = get_firebase_token()
    if not token:
        sys.exit(1)
    
    # Step 2: Check existing reports first
    existing_reports = get_reports(token)
    
    # Step 3: Start new analysis
    analysis_id = start_analysis(token)
    if not analysis_id:
        sys.exit(1)
    
    # Step 4: Poll for completion
    completed = poll_for_completion(token, analysis_id)
    
    # Step 5: Show updated reports
    if completed:
        print("\n🎉 Analysis completed! Fetching updated reports...")
        get_reports(token)
    else:
        print("\n⚠️  Analysis didn't complete in time. You can check later using:")
        print(f"   GET {API_BASE_URL}/ai-visibility/analysis/{analysis_id}/status")
        print(f"   GET {API_BASE_URL}/ai-visibility/reports")

if __name__ == "__main__":
    main()
