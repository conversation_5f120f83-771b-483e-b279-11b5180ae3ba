"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  BarChart3,
  Calendar,
  Download,
  Eye,
  Plus,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus,
  Wallet,
  Clock,
  Sparkles
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { centsToDollars } from "@/lib/utils";
import DashboardFooter from "@/components/DashboardFooter";
import { GEOWizard } from "@/components/geo-wizard/GEOWizard";

interface AIVisibilityReport {
  id: string;
  created_at: string;
  overall_score: number;
  ai_platform_coverage: number;
  brand_mention_frequency: number;
  sentiment_analysis: number;
  competitive_positioning: number;
  recommendation_likelihood: number;
  analyzed_platforms: string[];
  total_queries_tested: number;
  brand_name: string;
  website_analyzed: string;
  status: "completed" | "processing" | "failed";
  cost: number; // in cents
}

interface WalletData {
  total_available_balance: number; // in cents
}

export default function AIVisibilityReportPage() {
  const { user } = useAuth();
  const [reports, setReports] = useState<AIVisibilityReport[]>([]);
  const [walletData, setWalletData] = useState<WalletData>({ total_available_balance: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [showWizard, setShowWizard] = useState(false);



  // Fetch wallet balance
  const fetchWalletBalance = useCallback(async () => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/wallet`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setWalletData({
          total_available_balance: data.total_available_balance || 0
        });
      }
    } catch (error) {
      console.error("Error fetching wallet balance:", error);
    }
  }, [user]);

  // Fetch AI visibility reports history
  const fetchReports = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/ai-visibility/reports`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setReports(data.reports || []);
      }
    } catch (error) {
      console.error("Error fetching AI visibility reports:", error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Download report
  const downloadReport = async (reportId: string) => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/ai-visibility/reports/${reportId}/download`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `ai-visibility-report-${reportId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error("Error downloading report:", error);
    }
  };

  useEffect(() => {
    if (user) {
      fetchWalletBalance();
      fetchReports();
    }
  }, [user, fetchWalletBalance, fetchReports]);

  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const getTrendIcon = (current: number, previous?: number) => {
    if (!previous) return <Minus className="h-4 w-4 text-gray-400" />;
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  // Handle wizard completion
  const handleWizardComplete = async () => {
    setShowWizard(false);
    // Refresh reports and wallet balance
    await Promise.all([fetchReports(), fetchWalletBalance()]);
  };

  // Handle wizard cancel
  const handleWizardCancel = () => {
    setShowWizard(false);
  };

  if (showWizard) {
    return (
      <div className="min-h-screen bg-gray-50/30">
        <GEOWizard
          onComplete={handleWizardComplete}
          onCancel={handleWizardCancel}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
                  AI Visibility Reports
                </h1>
                <p className="mt-2 text-sm text-gray-600">
                  Track how your brand appears across AI platforms and get insights to improve visibility
                </p>
              </div>
              
              {/* Wallet Balance & Generate Button */}
              <div className="flex items-center gap-4">
                <Card className="border-0 shadow-sm bg-gray-50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-50 rounded-lg">
                        <Wallet className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Available Balance
                        </p>
                        <p className="text-lg font-bold text-green-600">
                          ${centsToDollars(walletData.total_available_balance).toFixed(2)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Button
                  onClick={() => setShowWizard(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Generate New Report
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-8">
        <div className="max-w-7xl mx-auto">
          

          {/* Reports History */}
          {(() => {
            console.log("Rendering condition - isLoading:", isLoading, "reports.length:", reports.length);
            return null;
          })()}
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-600">Loading reports...</span>
            </div>
          ) : reports.length === 0 ? (
            /* Empty State */
            <Card className="border-dashed border-2 border-gray-300">
              <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                <div className="p-4 bg-blue-50 rounded-full mb-4">
                  <Sparkles className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No AI Visibility Reports Yet
                </h3>
                <p className="text-gray-600 mb-6 max-w-md">
                  Generate your first AI visibility report to see how your brand appears across AI platforms 
                  and get actionable insights to improve your visibility.
                </p>
                <Button
                  onClick={() => setShowWizard(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Generate Report
                </Button>
              </CardContent>
            </Card>
          ) : (
            /* Reports List */
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Report History</h2>
                <Badge variant="outline" className="text-sm">
                  {reports.length} report{reports.length !== 1 ? 's' : ''}
                </Badge>
              </div>

              <div className="grid gap-6">
                {reports.map((report, index) => {
                  const previousReport = reports[index + 1];
                  
                  return (
                    <Card key={report.id} className="border-0 shadow-lg bg-white hover:shadow-xl transition-shadow">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-blue-50 rounded-lg">
                              <BarChart3 className="w-5 h-5 text-blue-600" />
                            </div>
                            <div>
                              <CardTitle className="text-lg font-semibold text-gray-900">
                                AI Visibility Report
                              </CardTitle>
                              <div className="flex items-center gap-4 mt-1">
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                  <Calendar className="w-4 h-4" />
                                  {formatDistanceToNow(new Date(report.created_at), { addSuffix: true })}
                                </div>
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                  <Clock className="w-4 h-4" />
                                  {report.analyzed_platforms.length} platforms analyzed
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge variant={getScoreBadgeVariant(report.overall_score)}>
                              {report.overall_score}/100
                            </Badge>
                            {getTrendIcon(report.overall_score, previousReport?.overall_score)}
                          </div>
                        </div>
                      </CardHeader>
                      
                      <CardContent className="space-y-6">
                        {/* Score Metrics */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">
                              {report.ai_platform_coverage}%
                            </div>
                            <div className="text-sm text-gray-600">Platform Coverage</div>
                            <Progress value={report.ai_platform_coverage} className="mt-2 h-2" />
                          </div>
                          
                          <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">
                              {report.brand_mention_frequency}%
                            </div>
                            <div className="text-sm text-gray-600">Mention Frequency</div>
                            <Progress value={report.brand_mention_frequency} className="mt-2 h-2" />
                          </div>
                          
                          <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">
                              {report.sentiment_analysis}%
                            </div>
                            <div className="text-sm text-gray-600">Sentiment Score</div>
                            <Progress value={report.sentiment_analysis} className="mt-2 h-2" />
                          </div>
                          
                          <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">
                              {report.recommendation_likelihood}%
                            </div>
                            <div className="text-sm text-gray-600">Recommendation Rate</div>
                            <Progress value={report.recommendation_likelihood} className="mt-2 h-2" />
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span>{report.total_queries_tested} queries tested</span>
                            <span>•</span>
                            <span>Cost: ${centsToDollars(report.cost).toFixed(2)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => downloadReport(report.id)}
                            >
                              <Download className="w-4 h-4 mr-2" />
                              Download
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(`/dashboard/brand/ai-visibility/${report.id}`, '_blank')}
                            >
                              <Eye className="w-4 h-4 mr-2" />
                              View Details
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      <DashboardFooter />
    </div>
  );
}
