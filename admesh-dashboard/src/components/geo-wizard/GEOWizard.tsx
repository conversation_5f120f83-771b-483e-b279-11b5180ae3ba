"use client";

import { useState, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  Zap,
  Brain,
  Settings,
  BarChart3
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

import { BrandInputStep } from "./steps/BrandInputStep";
import { PromptGenerationStep } from "./steps/PromptGenerationStep";
import { PromptCustomizationStep } from "./steps/PromptCustomizationStep";
import { AnalysisStep } from "./steps/AnalysisStep";

export interface BrandInfo {
  brand_name: string;
  website_url: string;
  description?: string;
  competitor_urls: string[];
  sitemap_url?: string;
  robots_url?: string;
  region: string;
  selected_models: string[];
}

export interface GeneratedPrompt {
  id: string;
  text: string;
  category?: string;
  isCustom?: boolean;
}

export interface AnalysisResult {
  report_id: string;
  status: "processing" | "completed" | "failed";
  overall_score?: number;
  model_results?: Record<string, any>;
  cost?: number;
}

interface GEOWizardProps {
  onComplete?: (result: AnalysisResult) => void;
  onCancel?: () => void;
}

const STEPS = [
  {
    id: 1,
    title: "Brand Information",
    description: "Enter your brand details",
    icon: <Settings className="h-4 w-4" />
  },
  {
    id: 2,
    title: "Generate Prompts",
    description: "AI creates test queries",
    icon: <Brain className="h-4 w-4" />
  },
  {
    id: 3,
    title: "Customize Prompts",
    description: "Review and edit queries",
    icon: <Zap className="h-4 w-4" />
  },
  {
    id: 4,
    title: "Run Analysis",
    description: "Test AI visibility",
    icon: <BarChart3 className="h-4 w-4" />
  }
];

export function GEOWizard({ onComplete, onCancel }: GEOWizardProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [brandInfo, setBrandInfo] = useState<BrandInfo>({
    brand_name: "",
    website_url: "",
    description: "",
    competitor_urls: [],
    sitemap_url: "",
    robots_url: "",
    region: "United States",
    selected_models: ["gpt-4o", "claude-4", "gemini-2.5", "perplexity-sonar"]
  });
  const [generatedPrompts, setGeneratedPrompts] = useState<GeneratedPrompt[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);

  const handleNext = useCallback(() => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleBrandInfoSubmit = useCallback((info: BrandInfo) => {
    setBrandInfo(info);
    handleNext();
  }, [handleNext]);

  const handlePromptsGenerated = useCallback((prompts: GeneratedPrompt[]) => {
    setGeneratedPrompts(prompts);
    handleNext();
  }, [handleNext]);

  const handlePromptsCustomized = useCallback((prompts: GeneratedPrompt[]) => {
    setGeneratedPrompts(prompts);
    handleNext();
  }, [handleNext]);

  const handleAnalysisComplete = useCallback((result: AnalysisResult) => {
    setAnalysisResult(result);
    onComplete?.(result);
  }, [onComplete]);

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <BrandInputStep
            initialData={brandInfo}
            onSubmit={handleBrandInfoSubmit}
            isLoading={isLoading}
          />
        );
      case 2:
        return (
          <PromptGenerationStep
            brandInfo={brandInfo}
            onComplete={handlePromptsGenerated}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
          />
        );
      case 3:
        return (
          <PromptCustomizationStep
            prompts={generatedPrompts}
            brandInfo={brandInfo}
            onComplete={handlePromptsCustomized}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
          />
        );
      case 4:
        return (
          <AnalysisStep
            brandInfo={brandInfo}
            prompts={generatedPrompts}
            onComplete={handleAnalysisComplete}
            isLoading={isLoading}
            setIsLoading={setIsLoading}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              AI Visibility Report Generator
            </h1>
            <p className="text-gray-600 mt-2">
              Analyze how your brand appears across AI platforms with our GEO (Generative Engine Optimization) tool
            </p>
          </div>
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-between mb-6">
          {STEPS.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className="flex items-center">
                <div
                  className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200
                    ${currentStep > step.id
                      ? "bg-green-500 border-green-500 text-white"
                      : currentStep === step.id
                      ? "bg-blue-500 border-blue-500 text-white"
                      : "bg-gray-100 border-gray-300 text-gray-500"
                    }
                  `}
                >
                  {currentStep > step.id ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    step.icon
                  )}
                </div>
                <div className="ml-3">
                  <div className="text-sm font-medium text-gray-900">
                    {step.title}
                  </div>
                  <div className="text-xs text-gray-500">
                    {step.description}
                  </div>
                </div>
              </div>
              {index < STEPS.length - 1 && (
                <div className="flex-1 mx-4">
                  <div
                    className={`
                      h-0.5 transition-all duration-200
                      ${currentStep > step.id ? "bg-green-500" : "bg-gray-300"}
                    `}
                  />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Progress Bar */}
        <Progress 
          value={(currentStep / STEPS.length) * 100} 
          className="h-2"
        />
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderStepContent()}
        </motion.div>
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex justify-between mt-8">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 1 || isLoading}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        {currentStep < STEPS.length && (
          <Button
            onClick={handleNext}
            disabled={isLoading}
          >
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        )}
      </div>
    </div>
  );
}
