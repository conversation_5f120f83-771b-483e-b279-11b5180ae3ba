"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Edit3, 
  RefreshCw, 
  Plus, 
  Trash2, 
  <PERSON>,
  Sparkles
} from "lucide-react";
import { motion } from "framer-motion";
import { BrandInfo, GeneratedPrompt } from "../GEOWizard";
import { useAuth } from "@/hooks/use-auth";

interface PromptCustomizationStepProps {
  prompts: GeneratedPrompt[];
  brandInfo: BrandInfo;
  onComplete: (prompts: GeneratedPrompt[]) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export function PromptCustomizationStep({
  prompts,
  brandInfo,
  onComplete,
  isLoading,
  setIsLoading
}: PromptCustomizationStepProps) {
  const { user } = useAuth();
  const [editablePrompts, setEditablePrompts] = useState<GeneratedPrompt[]>(prompts);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showJsonView, setShowJsonView] = useState(false);
  const [jsonText, setJsonText] = useState("");

  // Update prompt text
  const updatePrompt = (id: string, newText: string) => {
    setEditablePrompts(prev =>
      prev.map(prompt =>
        prompt.id === id ? { ...prompt, text: newText } : prompt
      )
    );
  };

  // Delete prompt
  const deletePrompt = (id: string) => {
    setEditablePrompts(prev => prev.filter(prompt => prompt.id !== id));
  };

  // Add custom prompt
  const addCustomPrompt = () => {
    const newPrompt: GeneratedPrompt = {
      id: `custom-${Date.now()}`,
      text: "",
      category: "custom",
      isCustom: true
    };
    setEditablePrompts(prev => [...prev, newPrompt]);
    setEditingId(newPrompt.id);
  };

  // Handle continue
  const handleContinue = () => {
    const validPrompts = editablePrompts.filter(p => p.text.trim().length > 0);
    onComplete(validPrompts);
  };

  return (
    <div className="bg-white rounded-lg border shadow-sm">
      <div className="p-6 border-b">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Edit3 className="h-5 w-5" />
          Customize Prompts
        </h3>
        <p className="text-sm text-gray-600">
          Review, edit, or add custom prompts to test your brand's AI visibility
        </p>
      </div>
      <div className="p-6 space-y-6">
        {/* Controls */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={addCustomPrompt}
            size="sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Custom Prompt
          </Button>

          <Button
            variant="outline"
            onClick={() => setShowJsonView(!showJsonView)}
            size="sm"
          >
            <Code className="h-4 w-4 mr-2" />
            {showJsonView ? "Visual View" : "JSON View"}
          </Button>
        </div>

        {/* Prompt List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium">Prompts ({editablePrompts.length})</span>
            <Badge variant="outline">
              {editablePrompts.filter(p => p.isCustom).length} custom
            </Badge>
          </div>

          <div className="space-y-3 max-h-96 overflow-y-auto">
            {editablePrompts.map((prompt, index) => (
              <motion.div
                key={prompt.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className={`
                  p-4 border rounded-lg transition-all
                  ${prompt.isCustom ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-white'}
                  ${editingId === prompt.id ? 'ring-2 ring-blue-500' : ''}
                `}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={prompt.isCustom ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {prompt.category}
                      </Badge>
                      {prompt.isCustom && (
                        <Badge variant="outline" className="text-xs">
                          Custom
                        </Badge>
                      )}
                    </div>

                    {editingId === prompt.id ? (
                      <div className="space-y-2">
                        <Textarea
                          value={prompt.text}
                          onChange={(e) => updatePrompt(prompt.id, e.target.value)}
                          rows={2}
                          className="text-sm"
                          placeholder="Enter your prompt..."
                        />
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => setEditingId(null)}
                          >
                            Save
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setEditingId(null)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-700">
                        {prompt.text || <span className="text-gray-400 italic">Empty prompt</span>}
                      </p>
                    )}
                  </div>

                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setEditingId(editingId === prompt.id ? null : prompt.id)}
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>

                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => deletePrompt(prompt.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Summary */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-start gap-2">
            <Sparkles className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-gray-900 mb-1">Ready for Analysis</h4>
              <p className="text-sm text-gray-600">
                {editablePrompts.filter(p => p.text.trim()).length} prompts will be tested across{" "}
                {brandInfo.selected_models.length} AI models ({brandInfo.selected_models.join(", ")})
              </p>
            </div>
          </div>
        </div>

        {/* Continue Button */}
        <Button
          onClick={handleContinue}
          disabled={editablePrompts.filter(p => p.text.trim()).length === 0}
          className="w-full"
        >
          Submit & Start Analysis
        </Button>
      </div>
    </div>
  );
}
