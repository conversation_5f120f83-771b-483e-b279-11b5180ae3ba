"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  BarChart3, 
  Brain, 
  CheckCircle, 
  Clock,
  Download,
  Eye,
  RefreshCw,
  Zap,
  TrendingUp,
  AlertCircle
} from "lucide-react";
import { motion } from "framer-motion";
import { BrandInfo, GeneratedPrompt, AnalysisResult } from "../GEOWizard";
import { useAuth } from "@/hooks/use-auth";

interface AnalysisStepProps {
  brandInfo: BrandInfo;
  prompts: GeneratedPrompt[];
  onComplete: (result: AnalysisResult) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

interface ModelProgress {
  model: string;
  status: "pending" | "running" | "evaluating" | "completed" | "failed";
  progress: number;
  prompts_completed: number;
  total_prompts: number;
}

interface AnalysisProgress {
  stage: "initializing" | "querying" | "evaluating" | "aggregating" | "completed";
  overall_progress: number;
  models: ModelProgress[];
  estimated_time_remaining: number;
}

const MODEL_NAMES: Record<string, string> = {
  "gpt-4o": "GPT-4o",
  "claude-4": "Claude 4",
  "gemini-2.5": "Gemini 2.5",
  "perplexity-sonar": "Perplexity Sonar"
};

export function AnalysisStep({ 
  brandInfo, 
  prompts, 
  onComplete, 
  isLoading, 
  setIsLoading 
}: AnalysisStepProps) {
  const { user } = useAuth();
  const [progress, setProgress] = useState<AnalysisProgress>({
    stage: "initializing",
    overall_progress: 0,
    models: brandInfo.selected_models.map(model => ({
      model,
      status: "pending",
      progress: 0,
      prompts_completed: 0,
      total_prompts: prompts.length
    })),
    estimated_time_remaining: 0
  });
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [hasStarted, setHasStarted] = useState(false);

  // Start analysis
  const startAnalysis = async () => {
    if (!user) {
      console.error("❌ No user found, cannot start analysis");
      return;
    }

    console.log("🚀 Starting analysis for user:", user.uid);
    setIsLoading(true);
    setHasStarted(true);

    try {
      const token = await user.getIdToken();
      console.log("🔑 Got Firebase token, starting analysis...");
      console.log("🌐 API URL:", `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/start-analysis`);
      console.log("📊 Analysis data:", {
        brand_info: brandInfo,
        prompts_count: prompts.length,
        selected_models: brandInfo.selected_models
      });

      // Submit analysis request
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/start-analysis`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          brand_info: brandInfo,
          prompts: prompts.map(p => ({
            id: p.id,
            text: p.text,
            category: p.category
          })),
          selected_models: brandInfo.selected_models
        }),
      });

      console.log("📡 Analysis API Response status:", response.status);

      if (response.ok) {
        const data = await response.json();
        console.log("✅ Analysis started successfully, ID:", data.analysis_id);

        // Start polling for progress
        pollAnalysisProgress(data.analysis_id);
      } else {
        const errorText = await response.text();
        console.error("❌ Analysis API Error:", response.status, errorText);
        throw new Error("Failed to start analysis");
      }
    } catch (error) {
      console.error("❌ Error starting analysis:", error);
      // Simulate analysis for demo
      console.log("🎭 Falling back to simulation mode");
      simulateAnalysis();
    }
  };

  // Poll analysis progress
  const pollAnalysisProgress = async (analysisId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        if (!user) return;

        const token = await user.getIdToken();
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/analysis/${analysisId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();

          if (data.progress) {
            setProgress(data.progress);
          }

          if (data.status === "completed" && data.result) {
            clearInterval(pollInterval);
            setResult({
              report_id: analysisId,
              status: "completed",
              overall_score: data.result.overall_score,
              model_results: data.result.model_results,
              cost: data.result.cost
            });
            setIsLoading(false);
            onComplete({
              report_id: analysisId,
              status: "completed",
              overall_score: data.result.overall_score,
              model_results: data.result.model_results,
              cost: data.result.cost
            });
          } else if (data.status === "failed") {
            console.error("Analysis failed:", data.error);
            clearInterval(pollInterval);
            simulateAnalysis();
          }
        } else {
          throw new Error("Failed to get analysis status");
        }
      } catch (error) {
        console.error("Error polling progress:", error);
        clearInterval(pollInterval);
        simulateAnalysis();
      }
    }, 3000);

    // Clear interval after 10 minutes to prevent infinite polling
    setTimeout(() => {
      clearInterval(pollInterval);
      if (!result) {
        console.log("Polling timeout, falling back to simulation");
        simulateAnalysis();
      }
    }, 600000);
  };

  // Simulate analysis for demo
  const simulateAnalysis = async () => {
    const totalSteps = 100;
    const stepDuration = 150; // ms per step

    for (let step = 0; step <= totalSteps; step++) {
      await new Promise(resolve => setTimeout(resolve, stepDuration));

      const overallProgress = (step / totalSteps) * 100;
      
      // Update stage based on progress
      let stage: AnalysisProgress["stage"] = "initializing";
      if (overallProgress > 10) stage = "querying";
      if (overallProgress > 70) stage = "evaluating";
      if (overallProgress > 90) stage = "aggregating";
      if (overallProgress >= 100) stage = "completed";

      // Update model progress
      const updatedModels = progress.models.map((model, index) => {
        const modelProgress = Math.max(0, overallProgress - (index * 10));
        let status: ModelProgress["status"] = "pending";
        
        if (modelProgress > 0) status = "running";
        if (modelProgress > 70) status = "evaluating";
        if (modelProgress >= 100) status = "completed";

        return {
          ...model,
          status,
          progress: Math.min(100, modelProgress),
          prompts_completed: Math.floor((modelProgress / 100) * prompts.length)
        };
      });

      setProgress({
        stage,
        overall_progress: overallProgress,
        models: updatedModels,
        estimated_time_remaining: Math.max(0, ((totalSteps - step) * stepDuration) / 1000)
      });

      if (overallProgress >= 100) {
        // Generate mock results
        const mockResult: AnalysisResult = {
          report_id: `analysis-${Date.now()}`,
          status: "completed",
          overall_score: Math.floor(Math.random() * 30) + 70, // 70-100
          model_results: brandInfo.selected_models.reduce((acc, model) => {
            acc[model] = {
              visibility_score: Math.floor(Math.random() * 40) + 60,
              mentions: Math.floor(Math.random() * prompts.length),
              positive_mentions: Math.floor(Math.random() * prompts.length * 0.8),
              top_ranking_count: Math.floor(Math.random() * prompts.length * 0.6)
            };
            return acc;
          }, {} as Record<string, any>),
          cost: 25.00
        };

        setResult(mockResult);
        setIsLoading(false);
        onComplete(mockResult);
        break;
      }
    }
  };

  // Auto-start analysis when component mounts and user is available
  useEffect(() => {
    if (!hasStarted && !isLoading && user) {
      console.log("🎯 Auto-starting analysis...");
      startAnalysis();
    }
  }, [hasStarted, isLoading, user]);

  const getStageMessage = (stage: string) => {
    switch (stage) {
      case "initializing":
        return "Initializing analysis pipeline...";
      case "querying":
        return "Querying AI models with your prompts...";
      case "evaluating":
        return "Evaluating brand visibility in responses...";
      case "aggregating":
        return "Aggregating results and generating insights...";
      case "completed":
        return "Analysis completed successfully!";
      default:
        return "Processing...";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600";
      case "running":
      case "evaluating":
        return "text-blue-600";
      case "failed":
        return "text-red-600";
      default:
        return "text-gray-500";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "running":
      case "evaluating":
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Run Visibility Analysis
        </CardTitle>
        <p className="text-sm text-gray-600">
          Testing your brand visibility across {brandInfo.selected_models.length} AI models with {prompts.length} prompts
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium">Overall Progress</span>
            <span className="text-sm text-gray-500">
              {Math.round(progress.overall_progress)}%
            </span>
          </div>
          
          <Progress value={progress.overall_progress} className="h-3" />
          
          <div className="flex items-center gap-2 text-sm">
            {progress.stage === "completed" ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
            )}
            <span>{getStageMessage(progress.stage)}</span>
            {progress.estimated_time_remaining > 0 && (
              <span className="text-gray-500">
                (~{Math.ceil(progress.estimated_time_remaining)}s remaining)
              </span>
            )}
          </div>
        </div>

        {/* Model Progress */}
        <div className="space-y-4">
          <h4 className="font-medium">Model Analysis Progress</h4>
          <div className="grid gap-3">
            {progress.models.map((model, index) => (
              <motion.div
                key={model.model}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-3 border rounded-lg bg-gray-50"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(model.status)}
                    <span className="font-medium">
                      {MODEL_NAMES[model.model] || model.model}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {model.status}
                    </Badge>
                  </div>
                  <span className="text-sm text-gray-500">
                    {model.prompts_completed}/{model.total_prompts}
                  </span>
                </div>
                <Progress value={model.progress} className="h-2" />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Results Preview */}
        {result && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <h4 className="font-medium">Analysis Results</h4>
              <Badge variant="default">Completed</Badge>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {result.overall_score}
                </div>
                <div className="text-sm text-blue-800">Overall Score</div>
              </div>
              
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {Object.keys(result.model_results || {}).length}
                </div>
                <div className="text-sm text-green-800">Models Tested</div>
              </div>
              
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {prompts.length}
                </div>
                <div className="text-sm text-purple-800">Prompts Tested</div>
              </div>
              
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  ${result.cost?.toFixed(2)}
                </div>
                <div className="text-sm text-orange-800">Analysis Cost</div>
              </div>
            </div>

            <div className="flex gap-3">
              <Button className="flex-1">
                <Eye className="h-4 w-4 mr-2" />
                View Full Report
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            </div>
          </motion.div>
        )}

        {/* Manual Start Button */}
        {!isLoading && !hasStarted && (
          <div className="flex gap-3">
            <Button
              onClick={() => {
                console.log("🔄 Manual analysis start button clicked");
                startAnalysis();
              }}
              className="flex-1"
            >
              <Brain className="h-4 w-4 mr-2" />
              Start Analysis
            </Button>
          </div>
        )}

        {/* Analysis Info */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-start gap-2">
            <Brain className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">How Analysis Works</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Each prompt is sent to selected AI models via OpenRouter</li>
                <li>• Responses are evaluated by our evaluation agent for brand visibility</li>
                <li>• Scores are aggregated across models and prompts</li>
                <li>• Final report includes insights and recommendations</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
