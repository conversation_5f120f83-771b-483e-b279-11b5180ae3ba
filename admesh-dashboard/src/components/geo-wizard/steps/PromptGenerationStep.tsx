"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Sparkles, 
  RefreshCw, 
  CheckCircle,
  Clock,
  Lightbulb
} from "lucide-react";
import { motion } from "framer-motion";
import { BrandInfo, GeneratedPrompt } from "../GEOWizard";
import { useAuth } from "@/hooks/use-auth";

interface PromptGenerationStepProps {
  brandInfo: BrandInfo;
  onComplete: (prompts: GeneratedPrompt[]) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

interface GenerationProgress {
  stage: string;
  progress: number;
  message: string;
}

const GENERATION_STAGES = [
  { stage: "analyzing", message: "Analyzing brand and website metadata...", duration: 2000 },
  { stage: "researching", message: "Researching industry and competitor landscape...", duration: 3000 },
  { stage: "generating", message: "Generating AI-style prompts using advanced LLM...", duration: 4000 },
  { stage: "optimizing", message: "Optimizing prompts for maximum visibility testing...", duration: 2000 },
  { stage: "complete", message: "Prompt generation complete!", duration: 500 }
];

export function PromptGenerationStep({ 
  brandInfo, 
  onComplete, 
  isLoading, 
  setIsLoading 
}: PromptGenerationStepProps) {
  const { user } = useAuth();
  const [progress, setProgress] = useState<GenerationProgress>({
    stage: "ready",
    progress: 0,
    message: "Ready to generate prompts"
  });
  const [generatedPrompts, setGeneratedPrompts] = useState<GeneratedPrompt[]>([]);
  const [hasStarted, setHasStarted] = useState(false);

  // Generate prompts using LLM
  const generatePrompts = async () => {
    if (!user) {
      console.error("❌ No user found, cannot generate prompts");
      return;
    }

    console.log("🚀 Starting prompt generation for user:", user.uid);
    setIsLoading(true);
    setHasStarted(true);

    try {
      // Simulate progress through stages
      for (let i = 0; i < GENERATION_STAGES.length; i++) {
        const stage = GENERATION_STAGES[i];
        setProgress({
          stage: stage.stage,
          progress: ((i + 1) / GENERATION_STAGES.length) * 100,
          message: stage.message
        });

        if (stage.stage === "generating") {
          // Call the actual API to generate prompts
          try {
            const token = await user.getIdToken();
            console.log("🔑 Got Firebase token, making API call...");
            console.log("🌐 API URL:", `${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/generate-prompts`);

            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/generate-prompts`, {
              method: "POST",
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                brand_name: brandInfo.brand_name,
                website_url: brandInfo.website_url,
                description: brandInfo.description,
                competitor_urls: brandInfo.competitor_urls,
                region: brandInfo.region,
                num_prompts: 10
              }),
            });

            console.log("📡 API Response status:", response.status);

            if (response.ok) {
              const data = await response.json();
              console.log("✅ API Success, got prompts:", data.prompts?.length || 0);
              console.log("🔍 API Response data:", data);
              setGeneratedPrompts(data.prompts || []);
            } else {
              const errorText = await response.text();
              console.error("❌ API Error:", response.status, errorText);
              throw new Error(`API Error ${response.status}: ${errorText}`);
            }
          } catch (apiError) {
            console.error("❌ API Call failed:", apiError);
            throw apiError;
          }
        } else {
          // Wait for stage duration
          await new Promise(resolve => setTimeout(resolve, stage.duration));
        }
      }

    } catch (error) {
      console.error("❌ Error generating prompts:", error);
      setProgress({
        stage: "error",
        progress: 0,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsLoading(false);
    }
  };



  // Infer industry from website/description
  const inferIndustry = (website: string, description?: string): string => {
    const text = `${website} ${description || ""}`.toLowerCase();
    
    if (text.includes("payment") || text.includes("fintech") || text.includes("finance")) {
      return "payment processing";
    }
    if (text.includes("ecommerce") || text.includes("shop") || text.includes("retail")) {
      return "ecommerce";
    }
    if (text.includes("software") || text.includes("saas") || text.includes("app")) {
      return "software";
    }
    if (text.includes("marketing") || text.includes("advertising")) {
      return "marketing";
    }
    if (text.includes("analytics") || text.includes("data")) {
      return "analytics";
    }
    
    return "business";
  };

  // Handle continue to next step
  const handleContinue = () => {
    onComplete(generatedPrompts);
  };

  // Auto-start generation when component mounts and user is available
  useEffect(() => {
    if (!hasStarted && !isLoading && user) {
      console.log("🎯 Auto-starting prompt generation...");
      generatePrompts();
    }
  }, [hasStarted, isLoading, user]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Auto-Generate Prompts
        </CardTitle>
        <p className="text-sm text-gray-600">
          Our AI is creating realistic test queries based on your brand information
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Generation Progress</span>
            <span className="text-sm text-gray-500">{Math.round(progress.progress)}%</span>
          </div>
          
          <Progress value={progress.progress} className="h-3" />
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            {isLoading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : progress.stage === "complete" ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <Clock className="h-4 w-4" />
            )}
            <span>{progress.message}</span>
          </div>
        </div>

        {/* Brand Context */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Using Brand Context:</h4>
          <div className="space-y-1 text-sm text-blue-800">
            <div>• Brand: <strong>{brandInfo.brand_name}</strong></div>
            <div>• Website: <strong>{brandInfo.website_url}</strong></div>
            <div>• Region: <strong>{brandInfo.region}</strong></div>
            {brandInfo.competitor_urls.length > 0 && (
              <div>• Competitors: <strong>{brandInfo.competitor_urls.length} analyzed</strong></div>
            )}
          </div>
        </div>

        {/* Generated Prompts Preview */}
        {generatedPrompts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-4"
          >
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-green-500" />
              <h4 className="font-medium">Generated Prompts Preview</h4>
              <Badge variant="secondary">{generatedPrompts.length} prompts</Badge>
            </div>
            
            <div className="grid gap-3 max-h-64 overflow-y-auto">
              {generatedPrompts.slice(0, 5).map((prompt, index) => (
                <motion.div
                  key={prompt.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-3 bg-gray-50 rounded-lg border"
                >
                  <div className="flex items-start gap-2">
                    <Badge variant="outline" className="text-xs">
                      {prompt.category}
                    </Badge>
                    <p className="text-sm flex-1">{prompt.text}</p>
                  </div>
                </motion.div>
              ))}
              
              {generatedPrompts.length > 5 && (
                <div className="text-center text-sm text-gray-500 py-2">
                  +{generatedPrompts.length - 5} more prompts...
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Insights */}
        {!isLoading && generatedPrompts.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="bg-green-50 p-4 rounded-lg"
          >
            <div className="flex items-start gap-2">
              <Lightbulb className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-green-900 mb-1">Generation Insights</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• Created {generatedPrompts.length} diverse prompts across multiple categories</li>
                  <li>• Optimized for {brandInfo.region} market context</li>
                  <li>• Included competitive and comparison queries</li>
                  <li>• Balanced direct brand mentions with discovery scenarios</li>
                </ul>
              </div>
            </div>
          </motion.div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {!isLoading && generatedPrompts.length > 0 && (
            <Button onClick={handleContinue} className="flex-1">
              Continue to Customize Prompts
            </Button>
          )}

          {!isLoading && !hasStarted && (
            <Button
              onClick={() => {
                console.log("🔄 Manual start button clicked");
                generatePrompts();
              }}
              className="flex-1"
            >
              <Brain className="h-4 w-4 mr-2" />
              Start Generation
            </Button>
          )}

          {!isLoading && hasStarted && (
            <Button
              variant="outline"
              onClick={() => {
                setHasStarted(false);
                setProgress({ stage: "ready", progress: 0, message: "Ready to generate prompts" });
                generatePrompts();
              }}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Regenerate
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
