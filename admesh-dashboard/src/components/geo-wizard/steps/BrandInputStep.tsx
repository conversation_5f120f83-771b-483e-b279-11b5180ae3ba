"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { 
  ChevronDown, 
  ChevronUp, 
  Globe, 
  Plus, 
  X, 
  HelpCircle,
  ExternalLink,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { BrandInfo } from "../GEOWizard";
import { useAuth } from "@/hooks/use-auth";

interface BrandInputStepProps {
  initialData: BrandInfo;
  onSubmit: (data: BrandInfo) => void;
  isLoading: boolean;
}

interface WebsitePreview {
  title?: string;
  description?: string;
  favicon?: string;
  isValid: boolean;
  error?: string;
}

const AI_MODELS = [
  { id: "gpt-4o", name: "GPT-4o", provider: "OpenAI" },
  { id: "claude-4", name: "Claude 4", provider: "Anthropic" },
  { id: "gemini-2.5", name: "Gemini 2.5", provider: "Google" },
  { id: "perplexity-sonar", name: "Perplexity Sonar", provider: "Perplexity" }
];

const REGIONS = [
  "United States",
  "United Kingdom", 
  "Canada",
  "Australia",
  "Germany",
  "France",
  "Japan",
  "Global"
];

export function BrandInputStep({ initialData, onSubmit, isLoading }: BrandInputStepProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState<BrandInfo>(initialData);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [websitePreview, setWebsitePreview] = useState<WebsitePreview | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Validate website URL and fetch preview
  const validateWebsite = async (url: string) => {
    if (!url) {
      setWebsitePreview(null);
      return;
    }

    setIsValidating(true);
    try {
      // Normalize URL
      const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;
      
      // In a real implementation, this would call an API to fetch website metadata
      // For now, we'll simulate the validation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setWebsitePreview({
        title: `${formData.brand_name || 'Website'} - Official Site`,
        description: "Official website with products and services",
        favicon: "/favicon.ico",
        isValid: true
      });
    } catch (error) {
      setWebsitePreview({
        isValid: false,
        error: "Unable to fetch website information"
      });
    } finally {
      setIsValidating(false);
    }
  };

  // Handle form field changes
  const handleFieldChange = (field: keyof BrandInfo, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Add competitor URL
  const addCompetitorUrl = () => {
    setFormData(prev => ({
      ...prev,
      competitor_urls: [...prev.competitor_urls, ""]
    }));
  };

  // Remove competitor URL
  const removeCompetitorUrl = (index: number) => {
    setFormData(prev => ({
      ...prev,
      competitor_urls: prev.competitor_urls.filter((_, i) => i !== index)
    }));
  };

  // Update competitor URL
  const updateCompetitorUrl = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      competitor_urls: prev.competitor_urls.map((url, i) => i === index ? value : url)
    }));
  };

  // Toggle model selection
  const toggleModel = (modelId: string) => {
    setFormData(prev => ({
      ...prev,
      selected_models: prev.selected_models.includes(modelId)
        ? prev.selected_models.filter(id => id !== modelId)
        : [...prev.selected_models, modelId]
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.brand_name.trim()) {
      newErrors.brand_name = "Brand name is required";
    }

    if (!formData.website_url.trim()) {
      newErrors.website_url = "Website URL is required";
    }

    if (formData.selected_models.length === 0) {
      newErrors.selected_models = "Please select at least one AI model";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Auto-fill brand information on component mount
  useEffect(() => {
    const fetchBrandProfile = async () => {
      console.log("Auto-fill check:", {
        hasUser: !!user,
        hasBrandName: !!formData.brand_name,
        hasWebsiteUrl: !!formData.website_url
      });

      if (!user || formData.brand_name || formData.website_url) {
        // Don't fetch if user is not available or form already has data
        console.log("Skipping auto-fill - conditions not met");
        return;
      }

      console.log("Starting auto-fill process...");
      console.log("API Base URL:", process.env.NEXT_PUBLIC_API_BASE_URL);
      setIsLoadingProfile(true);
      try {
        const token = await user.getIdToken();
        console.log("Got user token, making API call...");
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/geo/brand-profile`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        console.log("Auto-fill API response:", response.status);
        if (response.ok) {
          const profileData = await response.json();
          console.log("Auto-fill data received:", profileData);
          setFormData(prev => ({
            ...prev,
            brand_name: profileData.brand_name || prev.brand_name,
            website_url: profileData.website_url || prev.website_url,
            description: profileData.description || prev.description,
          }));
        } else {
          console.log("Auto-fill API error:", await response.text());
        }
      } catch (error) {
        console.error("Error fetching brand profile:", error);
      } finally {
        setIsLoadingProfile(false);
      }
    };

    fetchBrandProfile();
  }, [user, formData.brand_name, formData.website_url]);

  // Validate website when URL changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.website_url) {
        validateWebsite(formData.website_url);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [formData.website_url]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          Brand Information
          {isLoadingProfile && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          )}
        </CardTitle>
        <p className="text-sm text-gray-600">
          {isLoadingProfile
            ? "Loading your brand information..."
            : "Enter your brand details to generate targeted AI visibility prompts"
          }
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Required Fields */}
        <div className="space-y-4">
          <div>
            <Label htmlFor="brand_name">Brand Name *</Label>
            <Input
              id="brand_name"
              value={formData.brand_name}
              onChange={(e) => handleFieldChange("brand_name", e.target.value)}
              placeholder="Your Brand Name"
              className={errors.brand_name ? "border-red-500" : ""}
            />
            {errors.brand_name && (
              <p className="text-sm text-red-500 mt-1">{errors.brand_name}</p>
            )}
          </div>

          <div>
            <Label htmlFor="website_url">Website URL *</Label>
            <Input
              id="website_url"
              value={formData.website_url}
              onChange={(e) => handleFieldChange("website_url", e.target.value)}
              placeholder="yourbrand.com"
              className={errors.website_url ? "border-red-500" : ""}
            />
            {errors.website_url && (
              <p className="text-sm text-red-500 mt-1">{errors.website_url}</p>
            )}
            
            {/* Website Preview */}
            {isValidating && (
              <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-sm text-blue-600">Validating website...</span>
                </div>
              </div>
            )}
            
            {websitePreview && !isValidating && (
              <div className={`mt-2 p-3 rounded-lg ${websitePreview.isValid ? 'bg-green-50' : 'bg-red-50'}`}>
                <div className="flex items-start gap-2">
                  {websitePreview.isValid ? (
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600 mt-0.5" />
                  )}
                  <div className="flex-1">
                    {websitePreview.isValid ? (
                      <>
                        <p className="text-sm font-medium text-green-800">
                          {websitePreview.title}
                        </p>
                        <p className="text-xs text-green-600">
                          {websitePreview.description}
                        </p>
                      </>
                    ) : (
                      <p className="text-sm text-red-600">
                        {websitePreview.error}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Advanced Options Toggle */}
        <Button
          type="button"
          variant="ghost"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="w-full justify-between"
        >
          <span>Advanced Options</span>
          {showAdvanced ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>

        {/* Advanced Fields */}
        <AnimatePresence>
          {showAdvanced && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-4 border-t pt-4"
            >
              <div>
                <Label htmlFor="description">Brand Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleFieldChange("description", e.target.value)}
                  placeholder="Brief description of your brand or product..."
                  rows={3}
                />
              </div>

              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Label>Competitor URLs</Label>
                  <HelpCircle className="h-4 w-4 text-gray-400" />
                </div>
                <div className="space-y-2">
                  {formData.competitor_urls.map((url, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        value={url}
                        onChange={(e) => updateCompetitorUrl(index, e.target.value)}
                        placeholder="competitor.com"
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => removeCompetitorUrl(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  {formData.competitor_urls.length < 5 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addCompetitorUrl}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Competitor
                    </Button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="sitemap_url">Sitemap URL</Label>
                  <Input
                    id="sitemap_url"
                    value={formData.sitemap_url}
                    onChange={(e) => handleFieldChange("sitemap_url", e.target.value)}
                    placeholder="/sitemap.xml"
                  />
                </div>
                <div>
                  <Label htmlFor="robots_url">Robots.txt URL</Label>
                  <Input
                    id="robots_url"
                    value={formData.robots_url}
                    onChange={(e) => handleFieldChange("robots_url", e.target.value)}
                    placeholder="/robots.txt"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="region">Target Region</Label>
                <select
                  id="region"
                  value={formData.region}
                  onChange={(e) => handleFieldChange("region", e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  {REGIONS.map(region => (
                    <option key={region} value={region}>{region}</option>
                  ))}
                </select>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* AI Model Selection */}
        <div>
          <Label>AI Models to Test *</Label>
          <p className="text-sm text-gray-600 mb-3">
            Select which AI platforms to analyze your brand visibility across
          </p>
          <div className="grid grid-cols-2 gap-3">
            {AI_MODELS.map(model => (
              <div
                key={model.id}
                className={`
                  p-3 border rounded-lg cursor-pointer transition-all
                  ${formData.selected_models.includes(model.id)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}
                onClick={() => toggleModel(model.id)}
              >
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={formData.selected_models.includes(model.id)}
                    onChange={() => toggleModel(model.id)}
                  />
                  <div>
                    <div className="font-medium">{model.name}</div>
                    <div className="text-xs text-gray-500">{model.provider}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {errors.selected_models && (
            <p className="text-sm text-red-500 mt-1">{errors.selected_models}</p>
          )}
        </div>

        {/* Submit Button */}
        <Button
          onClick={handleSubmit}
          disabled={isLoading || isValidating}
          className="w-full"
        >
          {isLoading ? "Processing..." : "Continue to Prompt Generation"}
        </Button>
      </CardContent>
    </Card>
  );
}
