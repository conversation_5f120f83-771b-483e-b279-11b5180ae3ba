# 🚀 AI Visibility Analysis - Complete Usage Guide

## 🎯 **Overview**
The AI Visibility system analyzes how your brand appears across AI platforms (GPT<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>) and provides actionable insights to improve your visibility.

---

## 📋 **Complete API Flow**

### **1. Authentication**
First, get a Firebase token:

```bash
curl -X POST "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyDxBNmjuoMjkS5u8iad6PSB_5Lm7ggIkfY" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Gmk58@usa",
    "returnSecureToken": true
  }'
```

**Response:**
```json
{
  "idToken": "eyJhbGciOiJSUzI1NiIs...",
  "email": "<EMAIL>",
  "refreshToken": "...",
  "expiresIn": "3600"
}
```

### **2. Start Analysis**
Use the `idToken` to start an analysis:

```bash
curl -X POST "http://127.0.0.1:8000/ai-visibility/geo/start-analysis" \
  -H "Authorization: Bearer YOUR_ID_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "brand_info": {
      "brand_name": "Ramp",
      "website_url": "https://ramp.com",
      "description": "Ramp is a U.S.-based corporate card and expense management platform that helps businesses save time and money through automation, real-time spend tracking, and integrated finance tools.",
      "competitor_urls": [
        "https://www.expensify.com",
        "https://www.brex.com",
        "https://www.divvy.com"
      ],
      "sitemap_url": "https://ramp.com/sitemap.xml",
      "robots_url": "https://ramp.com/robots.txt",
      "region": "United States"
    },
    "prompts": [
      {
        "id": "2b034fcb-d5c9-4f70-9245-601aaf75ffa9",
        "text": "ramp vs expensify which is better",
        "category": "comparison",
        "isCustom": false
      },
      {
        "id": "caa29114-1b93-42df-aa74-75781db3076e",
        "text": "corporate card expense management software",
        "category": "recommendation",
        "isCustom": false
      },
      {
        "id": "9e9a803a-238c-4448-8204-e19ece8008d7",
        "text": "ramp business credit card pricing",
        "category": "pricing",
        "isCustom": false
      }
    ],
    "selected_models": ["gpt-4o"]
  }'
```

**Response:**
```json
{
  "analysis_id": "3fc24633-5341-4b3e-bf6b-e828c8dfb18d",
  "status": "processing",
  "message": "Analysis started successfully",
  "estimated_duration_minutes": 5,
  "total_model_calls": 3
}
```

### **3. Check Progress**
Poll the status endpoint to track progress:

```bash
curl -X GET "http://127.0.0.1:8000/ai-visibility/analysis/3fc24633-5341-4b3e-bf6b-e828c8dfb18d/status" \
  -H "Authorization: Bearer YOUR_ID_TOKEN"
```

**Response (In Progress):**
```json
{
  "analysis_id": "3fc24633-5341-4b3e-bf6b-e828c8dfb18d",
  "status": "running",
  "progress": {
    "stage": "model_execution",
    "overall_progress": 45.5,
    "models": [
      {
        "model": "gpt-4o",
        "status": "processing",
        "progress": 66.7,
        "prompts_completed": 2,
        "total_prompts": 3
      }
    ]
  },
  "created_at": "2024-01-15T10:30:00Z",
  "estimated_minutes_remaining": 2
}
```

**Response (Completed):**
```json
{
  "analysis_id": "3fc24633-5341-4b3e-bf6b-e828c8dfb18d",
  "status": "completed",
  "progress": {
    "stage": "completed",
    "overall_progress": 100
  },
  "created_at": "2024-01-15T10:30:00Z",
  "completed_at": "2024-01-15T10:35:00Z",
  "result_available": true
}
```

### **4. View Reports**
Once completed, fetch all reports:

```bash
curl -X GET "http://127.0.0.1:8000/ai-visibility/reports" \
  -H "Authorization: Bearer YOUR_ID_TOKEN"
```

**Response:**
```json
{
  "reports": [
    {
      "id": "3fc24633-5341-4b3e-bf6b-e828c8dfb18d",
      "created_at": "2024-01-15T10:35:00Z",
      "overall_score": 78,
      "ai_platform_coverage": 100,
      "brand_mention_frequency": 67,
      "sentiment_analysis": 85,
      "competitive_positioning": 78,
      "recommendation_likelihood": 67,
      "analyzed_platforms": ["gpt-4o"],
      "total_queries_tested": 3,
      "brand_name": "Ramp",
      "website_analyzed": "https://ramp.com",
      "status": "completed",
      "cost": 150
    }
  ],
  "total_count": 1
}
```

---

## 🔄 **Status Flow**

### **Analysis Statuses:**
- `processing` → Analysis is being initialized
- `running` → AI models are being called and evaluated
- `completed` → Analysis finished successfully
- `failed` → Analysis encountered an error

### **Progress Tracking:**
- `overall_progress`: 0-100% completion
- `stage`: Current processing stage
- `models`: Per-model progress breakdown

---

## 📊 **Report Metrics Explained**

### **Core Metrics:**
- **Overall Score (0-100)**: Weighted average of all visibility metrics
- **AI Platform Coverage**: Percentage of models that mentioned your brand
- **Brand Mention Frequency**: How often your brand appears in responses
- **Sentiment Analysis**: Percentage of positive mentions
- **Competitive Positioning**: How you rank vs competitors
- **Recommendation Likelihood**: How often you're recommended

### **Detailed Analysis:**
Each report includes:
- **Natural AI Answers**: Actual responses from each model
- **Structured Evaluations**: JSON data with mention, rank, sentiment, strength
- **Model-Specific Breakdowns**: Performance per AI platform
- **Cost Analysis**: Token usage and costs
- **Improvement Recommendations**: Actionable insights

---

## 🧪 **Testing with the Script**

Run the provided test script:

```bash
python test_ai_visibility_flow.py
```

This will:
1. ✅ Get Firebase authentication token
2. 🚀 Start a new AI Visibility analysis
3. ⏳ Poll for completion with progress updates
4. 📊 Display all completed reports

---

## 🎯 **Dashboard Integration**

The reports automatically appear in the AdMesh dashboard at:
- **URL**: `/dashboard/brand/ai-visibility`
- **Features**: 
  - View all historical reports
  - Start new analyses via wizard
  - Track real-time progress
  - Download detailed results

---

## 🔧 **Advanced Usage**

### **Multiple Models:**
```json
{
  "selected_models": ["gpt-4o", "claude-4", "gemini-2.5", "perplexity-sonar"]
}
```

### **Custom Prompts:**
```json
{
  "prompts": [
    {
      "id": "custom-1",
      "text": "Your custom analysis prompt here",
      "category": "custom",
      "isCustom": true
    }
  ]
}
```

### **Detailed Results:**
```bash
curl -X GET "http://127.0.0.1:8000/ai-visibility/geo/analysis/ANALYSIS_ID" \
  -H "Authorization: Bearer YOUR_ID_TOKEN"
```

---

## 🎉 **What You Get**

### **For Each Prompt × Model Combination:**
- ✅ Natural AI response (how the model actually answers)
- ✅ Structured evaluation (mentioned, rank, sentiment, strength)
- ✅ Performance metrics and token usage

### **Aggregated Insights:**
- ✅ Overall brand visibility score
- ✅ Model-specific performance breakdown
- ✅ Competitive positioning analysis
- ✅ Actionable improvement recommendations
- ✅ Cost analysis and ROI metrics

**🚀 Your AI Visibility analysis system is now fully operational!**
