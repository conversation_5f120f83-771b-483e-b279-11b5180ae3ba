"""
AI Visibility Report API endpoints for analyzing brand visibility across AI platforms.
Provides comprehensive analysis of brand mentions, sentiment, and recommendation likelihood.
"""

from fastapi import APIRouter, HTTPException, Depends, Response
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import logging
from datetime import datetime, timedelta
import uuid
import random
from io import BytesIO
import json
import os
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from auth.deps import require_role
from auth.firebase_auth import get_current_user
from firebase.config import get_db
from google.cloud import firestore

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ai-visibility", tags=["ai-visibility"])

# Add GEO router for new wizard endpoints under ai-visibility
geo_router = APIRouter(prefix="/ai-visibility", tags=["ai-visibility"])

# OpenRouter configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1/chat/completions"

# AI Model configurations for OpenRouter
AI_MODELS = {
    "gpt-4o": "openai/gpt-4o",
    "claude-4": "anthropic/claude-opus-4",
    "gemini-2.5": "google/gemini-2.5-flash",
    "perplexity-sonar": "perplexity/sonar"
}

# Cost per model (in cents per 1000 tokens)
MODEL_COSTS = {
    "gpt-4o": 5,
    "claude-4": 15,
    "gemini-2.5": 3,
    "perplexity-sonar": 10
}

def get_site_url():
    """Get the site URL for OpenRouter attribution"""
    env = os.getenv("ENV", "development")
    if env == "production":
        return os.getenv("PROD_SITE_URL", "https://api.useadmesh.com")
    return os.getenv("SITE_URL", "http://127.0.0.1:8000")

def create_production_ai_visibility_prompt(brand_name: str, brand_description: str, analysis_prompt: str) -> str:
    """Create the production-ready AI visibility prompt with structured evaluation"""
    return f"""You are an AI assistant that specializes in analyzing brand visibility across AI-generated recommendations, search results, and comparisons.

## BRAND CONTEXT ##
Brand Name: {brand_name}
Brand Description: {brand_description}

## CURRENT ANALYSIS PROMPT ##
{analysis_prompt}

---

## YOUR TASK ##
You must produce TWO things in your output:

1. **Natural AI Answer**
   - Answer the analysis prompt as if you are responding to a real user asking for advice or recommendations.
   - Write naturally and credibly, as if you are an industry expert.
   - Include multiple relevant options, ranked or listed where appropriate.
   - Ensure your response reflects realistic competitive positioning in the market.

2. **Structured JSON Brand Visibility Evaluation**
   - After your written answer, output a **single valid JSON object** that evaluates **{brand_name}**'s presence in your answer.
   - This JSON must strictly match the following schema:

```json
{{
  "mentioned": true,
  "rank": 2,
  "sentiment": "positive",
  "strength": "strong"
}}
```

---

## FIELD DEFINITIONS

* **mentioned** (boolean): `true` if {brand_name} is explicitly mentioned in your answer; `false` otherwise.
* **rank** (integer | null): 1-based position of {brand_name} in your list of recommendations. `null` if not mentioned.
* **sentiment** ("positive" | "neutral" | "negative"): Overall tone towards {brand_name} in your answer.
* **strength** ("strong" | "moderate" | "weak"): How strongly you recommend {brand_name} compared to competitors.

---

## RULES

* ALWAYS answer the prompt naturally first, then output the JSON on a **new line**.
* The JSON must be **valid** and **parsable** without extra text, comments, or formatting.
* If the brand is not mentioned, set `mentioned` to false, `rank` to null, and `strength` to `"weak"`.
* Do not explain the JSON; just output it.
* Avoid hallucinating features not in the brand description unless they are known facts.

---

## EXAMPLE OUTPUT

Example for brand Ramp.com and prompt "Best corporate card providers for startups in the USA":

Top corporate card providers for startups:

1. Brex – Excellent rewards and integrations.
2. Ramp – Great for cost-conscious startups with automation features.
3. American Express – Strong travel benefits.
4. Divvy – Solid budgeting tools.
5. Stripe Corporate Card – Easy integration for online-first companies.

{{
"mentioned": true,
"rank": 2,
"sentiment": "positive",
"strength": "strong"
}}"""

def parse_ai_visibility_response(response_content: str) -> dict:
    """Parse the AI response to extract both the natural answer and structured evaluation"""
    try:
        # Split the response to find the JSON part
        lines = response_content.strip().split('\n')

        # Find the last line that looks like JSON
        json_line = None
        answer_lines = []

        for i, line in enumerate(lines):
            line = line.strip()
            if line.startswith('{') and line.endswith('}'):
                # This looks like our JSON evaluation
                json_line = line
                answer_lines = lines[:i]
                break

        if not json_line:
            # Try to find JSON in the last few lines
            for i in range(len(lines) - 1, max(0, len(lines) - 5), -1):
                line = lines[i].strip()
                if line.startswith('{') and line.endswith('}'):
                    json_line = line
                    answer_lines = lines[:i]
                    break

        if not json_line:
            # Fallback: look for JSON anywhere in the response
            import re
            json_match = re.search(r'\{[^{}]*"mentioned"[^{}]*\}', response_content)
            if json_match:
                json_line = json_match.group()
                # Extract answer as everything before the JSON
                json_start = response_content.find(json_line)
                answer_lines = response_content[:json_start].strip().split('\n')

        # Parse the natural answer
        natural_answer = '\n'.join(answer_lines).strip()

        # Parse the JSON evaluation
        if json_line:
            try:
                evaluation = json.loads(json_line)

                # Validate required fields
                required_fields = ['mentioned', 'rank', 'sentiment', 'strength']
                for field in required_fields:
                    if field not in evaluation:
                        evaluation[field] = None if field == 'rank' else 'unknown'

                # Validate field types and values
                if not isinstance(evaluation['mentioned'], bool):
                    evaluation['mentioned'] = False

                if evaluation['rank'] is not None and not isinstance(evaluation['rank'], int):
                    evaluation['rank'] = None

                if evaluation['sentiment'] not in ['positive', 'neutral', 'negative']:
                    evaluation['sentiment'] = 'neutral'

                if evaluation['strength'] not in ['strong', 'moderate', 'weak']:
                    evaluation['strength'] = 'weak'

                return {
                    "success": True,
                    "natural_answer": natural_answer,
                    "evaluation": evaluation
                }
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON evaluation: {e}")
                logger.error(f"JSON line: {json_line}")

        # Fallback: return just the natural answer with default evaluation
        return {
            "success": True,
            "natural_answer": natural_answer or response_content,
            "evaluation": {
                "mentioned": False,
                "rank": None,
                "sentiment": "neutral",
                "strength": "weak"
            }
        }

    except Exception as e:
        logger.error(f"Error parsing AI visibility response: {e}")
        return {
            "success": False,
            "error": str(e),
            "natural_answer": response_content,
            "evaluation": {
                "mentioned": False,
                "rank": None,
                "sentiment": "neutral",
                "strength": "weak"
            }
        }

async def call_openrouter_model(prompt: str, model: str, max_retries: int = 2) -> dict:
    """Call OpenRouter API for a specific model with limited retries"""
    # Require OpenRouter API key
    if not OPENROUTER_API_KEY:
        logger.error("OpenRouter API key not configured")
        return {
            "success": False,
            "error": "OpenRouter API key not configured. Please set OPENROUTER_API_KEY environment variable."
        }

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": get_site_url(),
        "X-Title": "AdMesh AI Visibility Analysis"
    }

    payload = {
        "model": AI_MODELS.get(model, model),
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.3,  # Lower temperature for more consistent results
        "max_tokens": 2000
    }

    # Add structured output for JSON responses if the model supports it
    if model == "gpt-4o" and "json" in prompt.lower():
        payload["response_format"] = {
            "type": "json_object"
        }
        logger.info(f"🔧 Added JSON response format for {model}")

    logger.info(f"🚀 Making OpenRouter API call to model: {AI_MODELS.get(model, model)} (max_retries: {max_retries})")
    logger.info(f"📝 Prompt length: {len(prompt)} characters")
    logger.info(f"🔑 API Key configured: {'Yes' if OPENROUTER_API_KEY else 'No'}")
    logger.info(f"🌐 Base URL: {OPENROUTER_BASE_URL}")
    logger.info(f"📦 Payload model: {payload['model']}")
    logger.info(f"🌡️ Temperature: {payload['temperature']}")
    logger.info(f"🎯 Max tokens: {payload['max_tokens']}")

    for attempt in range(max_retries):
        try:
            logger.info(f"🔄 Attempt {attempt + 1}/{max_retries}")

            # Log request details
            logger.info(f"📤 Sending POST request to: {OPENROUTER_BASE_URL}")
            logger.info(f"📋 Headers: {dict(headers)}")

            response = requests.post(OPENROUTER_BASE_URL, headers=headers, json=payload, timeout=60)

            logger.info(f"📊 OpenRouter API response status: {response.status_code}")
            logger.info(f"📋 Response headers: {dict(response.headers)}")

            # Log response content preview
            if response.text:
                logger.info(f"📄 Response preview (first 200 chars): {response.text[:200]}...")
            else:
                logger.warning("⚠️ Empty response body")

            response.raise_for_status()
            result = response.json()

            logger.info(f"✅ JSON parsing successful")
            logger.info(f"🔍 Response keys: {list(result.keys())}")

            if "choices" in result and result["choices"]:
                content = result["choices"][0]["message"]["content"]
                logger.info(f"✅ OpenRouter API success - content length: {len(content) if content else 0}")

                if content:
                    logger.info(f"📝 Content preview: {content[:100]}...")
                else:
                    logger.warning("⚠️ Content is empty")

                return {
                    "success": True,
                    "content": content,
                    "usage": result.get("usage", {}),
                    "model": model
                }
            else:
                logger.error("❌ No choices in response")
                logger.error(f"Full response: {result}")
                raise ValueError("No choices in OpenRouter response")

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ OpenRouter API error for model {model} (attempt {attempt + 1}): {str(e)}")
            logger.error(f"🔍 Exception type: {type(e).__name__}")

            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"📊 Response status: {e.response.status_code}")
                logger.error(f"📄 Response text: {e.response.text}")
                logger.error(f"📋 Response headers: {dict(e.response.headers)}")
            else:
                logger.error("❌ No response object available")

            # If this is the last attempt, return error
            if attempt == max_retries - 1:
                logger.error(f"💀 All {max_retries} attempts failed, giving up")
                return {
                    "success": False,
                    "error": f"OpenRouter API call failed after {max_retries} attempts: {str(e)}"
                }
            else:
                logger.info(f"⏳ Retrying in 1 second...")
                await asyncio.sleep(1)
        except Exception as e:
            logger.error(f"❌ Unexpected error in attempt {attempt + 1}: {str(e)}")
            logger.error(f"🔍 Exception type: {type(e).__name__}")

            if attempt == max_retries - 1:
                logger.error(f"💀 All {max_retries} attempts failed due to unexpected error")
                return {
                    "success": False,
                    "error": f"Unexpected error after {max_retries} attempts: {str(e)}"
                }
            else:
                logger.info(f"⏳ Retrying in 1 second...")
                await asyncio.sleep(1)

def create_evaluation_prompt(prompt: str, response: str, brand_name: str, competitors: list = None) -> str:
    """Create a structured prompt for evaluating brand visibility"""
    competitors_text = ""
    if competitors:
        competitors_text = f"Competitor Brands: {', '.join(competitors)}"

    return f"""
Evaluate the following AI model response for brand visibility and recommendation quality.

Prompt: "{prompt}"
Response: "{response}"
Target Brand: "{brand_name}"
{competitors_text}

Analyze the response and return a JSON object with the following structure:
{{
    "mentioned": true/false,
    "ranking": "first" | "second" | "third" | "mentioned" | "not_mentioned",
    "tone": "positive" | "neutral" | "negative",
    "context": "primary_recommendation" | "alternative" | "comparison" | "brief_mention" | "not_mentioned",
    "reasoning": "Detailed explanation of the evaluation",
    "improvement_suggestion": "Specific suggestion for improving brand visibility",
    "confidence_score": 0.0-1.0,
    "competitor_analysis": {{
        "competitors_mentioned": ["competitor1", "competitor2"],
        "brand_vs_competitors": "better" | "equal" | "worse" | "not_compared"
    }}
}}

Be precise and objective in your evaluation. Focus on factual analysis of brand positioning and recommendation quality.
"""

async def evaluate_brand_visibility(prompt: str, response: str, brand_name: str, competitors: list = None) -> dict:
    """Evaluate brand visibility in a model response using GPT-4o"""
    evaluation_prompt = create_evaluation_prompt(prompt, response, brand_name, competitors)

    try:
        # Use limited retries for evaluation
        result = await call_openrouter_model(evaluation_prompt, "claude-4", max_retries=2)
        if result["success"]:
            # Parse the JSON response
            evaluation_data = json.loads(result["content"])
            return {
                "success": True,
                "evaluation": evaluation_data,
                "evaluator_usage": result.get("usage", {})
            }
        else:
            logger.error(f"Evaluation failed: {result.get('error', 'Unknown error')}")
            # Return mock evaluation to prevent analysis failure
            return {
                "success": True,
                "evaluation": {
                    "brand_mentioned": False,
                    "brand_visibility_score": 0,
                    "recommendation_likelihood": "low",
                    "sentiment": "neutral",
                    "ranking_position": 10,
                    "context_relevance": "low",
                    "explanation": "Evaluation failed - using fallback scoring"
                },
                "evaluator_usage": {"total_tokens": 0}
            }
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse evaluation JSON: {str(e)}")
        # Return mock evaluation instead of failing
        return {
            "success": True,
            "evaluation": {
                "brand_mentioned": False,
                "brand_visibility_score": 0,
                "recommendation_likelihood": "low",
                "sentiment": "neutral",
                "ranking_position": 10,
                "context_relevance": "low",
                "explanation": "JSON parsing failed - using fallback scoring"
            },
            "evaluator_usage": {"total_tokens": 0}
        }
    except Exception as e:
        logger.error(f"Evaluation error: {str(e)}")
        # Return mock evaluation instead of failing
        return {
            "success": True,
            "evaluation": {
                "brand_mentioned": False,
                "brand_visibility_score": 0,
                "recommendation_likelihood": "low",
                "sentiment": "neutral",
                "ranking_position": 10,
                "context_relevance": "low",
                "explanation": f"Evaluation error: {str(e)}"
            },
            "evaluator_usage": {"total_tokens": 0}
        }

class AIVisibilityMetrics(BaseModel):
    overall_score: int
    platform_coverage: float
    mention_frequency: int
    sentiment_score: float
    recommendation_likelihood: float
    cost: float
    trend_vs_previous: Optional[str] = None

class AIVisibilityReport(BaseModel):
    id: str
    created_at: str
    overall_score: int
    ai_platform_coverage: int
    brand_mention_frequency: int
    sentiment_analysis: int
    competitive_positioning: int
    recommendation_likelihood: int
    analyzed_platforms: List[str]
    total_queries_tested: int
    brand_name: str
    website_analyzed: str
    status: str
    cost: int  # in cents
    metrics: Optional[AIVisibilityMetrics] = None
    platforms_analyzed: Optional[List[str]] = None
    detailed_analysis: Optional[Dict[str, Any]] = None

class GenerateReportRequest(BaseModel):
    brand_id: Optional[str] = None  # If not provided, use authenticated user's brand

class AIVisibilityReportsResponse(BaseModel):
    reports: List[AIVisibilityReport]
    total_count: int

@router.get("/reports", response_model=AIVisibilityReportsResponse)
async def get_ai_visibility_reports(
    current_user: dict = Depends(get_current_user),
    limit: int = 10,
    offset: int = 0
):
    """Get AI visibility reports history for the authenticated brand."""
    try:
        db = get_db()

        # Get brand ID from user
        brand_id = current_user.get("uid")
        if not brand_id:
            raise HTTPException(status_code=400, detail="Brand ID not found")

        all_reports = []

        # 1. Query legacy ai_visibility_reports collection
        reports_ref = db.collection("ai_visibility_reports")
        legacy_query = reports_ref.where("brand_id", "==", brand_id)
        legacy_docs = legacy_query.get()

        for doc in legacy_docs:
            report_data = doc.to_dict()
            report_data["id"] = doc.id

            # Convert Firestore timestamp to ISO string
            if "created_at" in report_data and hasattr(report_data["created_at"], "isoformat"):
                report_data["created_at"] = report_data["created_at"].isoformat()
            elif "created_at" in report_data:
                report_data["created_at"] = str(report_data["created_at"])

            all_reports.append(report_data)

        # 2. Query new geo_analyses collection for completed analyses
        geo_ref = db.collection("geo_analyses")
        geo_query = geo_ref.where("user_id", "==", brand_id).where("status", "==", "completed")
        geo_docs = geo_query.get()

        for doc in geo_docs:
            geo_data = doc.to_dict()

            # Convert GEO analysis to AI Visibility report format
            brand_info = geo_data.get("brand_info", {})
            result = geo_data.get("result", {})

            # Calculate metrics from GEO analysis result
            overall_score = result.get("overall_score", 0)
            model_results = result.get("model_results", {})

            # Calculate aggregated metrics
            total_mentions = sum(model.get("mentions", 0) for model in model_results.values())
            total_prompts = sum(model.get("total_prompts", 0) for model in model_results.values())
            total_positive = sum(model.get("positive_mentions", 0) for model in model_results.values())

            ai_platform_coverage = len([m for m in model_results.values() if m.get("mentions", 0) > 0]) / max(len(model_results), 1) * 100
            brand_mention_frequency = (total_mentions / max(total_prompts, 1)) * 100
            sentiment_analysis = (total_positive / max(total_mentions, 1)) * 100 if total_mentions > 0 else 0

            # Handle cost conversion (could be in dollars or cents)
            cost_analysis = result.get("cost_analysis", {})
            total_cost = cost_analysis.get("total_cost_cents", 0)
            if total_cost == 0:
                # Fallback to total_cost_dollars if cents not available
                total_cost_dollars = cost_analysis.get("total_cost_dollars", 0)
                total_cost = int(float(total_cost_dollars) * 100)  # Convert dollars to cents
            else:
                total_cost = int(float(total_cost))  # Ensure it's an integer

            # Convert to expected format (flat structure for dashboard compatibility)
            converted_report = {
                "id": doc.id,
                "created_at": geo_data.get("completed_at", geo_data.get("created_at", "")),
                "overall_score": overall_score,
                "ai_platform_coverage": int(ai_platform_coverage),
                "brand_mention_frequency": int(brand_mention_frequency),
                "sentiment_analysis": int(sentiment_analysis),
                "competitive_positioning": overall_score,  # Use overall score as proxy
                "recommendation_likelihood": int(brand_mention_frequency),  # Use mention frequency as proxy
                "analyzed_platforms": list(model_results.keys()),
                "total_queries_tested": len(geo_data.get("prompts", [])),
                "brand_name": brand_info.get("brand_name", ""),
                "website_analyzed": brand_info.get("website_url", ""),
                "status": "completed",
                "cost": total_cost,
                # Optional nested metrics for API compatibility
                "metrics": {
                    "overall_score": overall_score,
                    "platform_coverage": float(ai_platform_coverage),
                    "mention_frequency": int(brand_mention_frequency),
                    "sentiment_score": float(sentiment_analysis),
                    "recommendation_likelihood": float(brand_mention_frequency),
                    "cost": float(total_cost)
                },
                "platforms_analyzed": list(model_results.keys()),
                "detailed_analysis": result
            }

            # Convert timestamp
            if isinstance(converted_report["created_at"], str):
                pass  # Already a string
            elif hasattr(converted_report["created_at"], "isoformat"):
                converted_report["created_at"] = converted_report["created_at"].isoformat()
            else:
                converted_report["created_at"] = str(converted_report["created_at"])

            all_reports.append(converted_report)

        # Sort all reports by created_at (newest first)
        all_reports.sort(key=lambda x: x.get("created_at", ""), reverse=True)

        # Apply pagination
        paginated_reports = all_reports[offset:offset + limit]

        # Convert to Pydantic models
        formatted_reports = []
        for report_data in paginated_reports:
            try:
                # Ensure all required fields are present with default values
                report_data.setdefault("overall_score", 0)
                report_data.setdefault("ai_platform_coverage", 0)
                report_data.setdefault("brand_mention_frequency", 0)
                report_data.setdefault("sentiment_analysis", 0)
                report_data.setdefault("competitive_positioning", 0)
                report_data.setdefault("recommendation_likelihood", 0)
                report_data.setdefault("analyzed_platforms", [])
                report_data.setdefault("total_queries_tested", 0)
                report_data.setdefault("brand_name", "")
                report_data.setdefault("website_analyzed", "")
                report_data.setdefault("status", "completed")
                report_data.setdefault("cost", 0)

                # Ensure metrics object has all required fields
                if "metrics" not in report_data or not report_data["metrics"]:
                    report_data["metrics"] = {}

                metrics = report_data["metrics"]
                metrics.setdefault("overall_score", report_data.get("overall_score", 0))
                metrics.setdefault("platform_coverage", float(report_data.get("ai_platform_coverage", 0)))
                metrics.setdefault("mention_frequency", report_data.get("brand_mention_frequency", 0))
                metrics.setdefault("sentiment_score", float(report_data.get("sentiment_analysis", 0)))
                metrics.setdefault("recommendation_likelihood", float(report_data.get("recommendation_likelihood", 0)))
                metrics.setdefault("cost", float(report_data.get("cost", 0)))

                formatted_reports.append(AIVisibilityReport(**report_data))
            except Exception as e:
                logger.error(f"Error formatting report {report_data.get('id', 'unknown')}: {e}")
                logger.error(f"Report data keys: {list(report_data.keys()) if isinstance(report_data, dict) else 'Not a dict'}")
                continue

        return AIVisibilityReportsResponse(
            reports=formatted_reports,
            total_count=len(all_reports)
        )

    except Exception as e:
        logger.error(f"Error fetching AI visibility reports: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch reports")

@router.post("/generate")
async def generate_ai_visibility_report(
    request: GenerateReportRequest,
    current_user: dict = Depends(get_current_user)
):
    """Generate a new AI visibility report for the authenticated brand."""
    try:
        db = get_db()

        # Get brand ID from user
        brand_id = current_user.get("uid")
        if not brand_id:
            raise HTTPException(status_code=400, detail="Brand ID not found")

        # Check if user has sufficient wallet balance
        wallet_ref = db.collection("wallets").document(brand_id)
        wallet_doc = wallet_ref.get()

        if not wallet_doc.exists:
            raise HTTPException(status_code=400, detail="Wallet not found")

        wallet_data = wallet_doc.to_dict()
        available_balance = wallet_data.get("available_balance", 0)

        # AI visibility report cost (in cents)
        report_cost = 2500  # $25.00

        if available_balance < report_cost:
            raise HTTPException(
                status_code=400,
                detail=f"Insufficient balance. Report costs ${report_cost/100:.2f}, available: ${available_balance/100:.2f}"
            )

        # Get brand information
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        brand_data = brand_doc.to_dict()
        brand_name = brand_data.get("name", "Unknown Brand")

        # Generate report ID
        report_id = str(uuid.uuid4())

        # Simulate AI visibility analysis (in production, this would call actual AI services)
        await asyncio.sleep(2)  # Simulate processing time

        # Generate mock metrics with some randomization
        overall_score = random.randint(65, 95)
        platform_coverage = round(random.uniform(0.6, 0.95), 2)
        mention_frequency = random.randint(15, 150)
        sentiment_score = round(random.uniform(0.6, 0.9), 2)
        recommendation_likelihood = round(random.uniform(0.4, 0.8), 2)

        # Get previous report for trend comparison
        previous_reports = db.collection("ai_visibility_reports").where("brand_id", "==", brand_id).order_by("created_at", direction=firestore.Query.DESCENDING).limit(1).get()
        trend_vs_previous = None

        if previous_reports:
            prev_report = previous_reports[0].to_dict()
            prev_score = prev_report.get("metrics", {}).get("overall_score", overall_score)
            if overall_score > prev_score:
                trend_vs_previous = "up"
            elif overall_score < prev_score:
                trend_vs_previous = "down"
            else:
                trend_vs_previous = "stable"

        # Create report data
        report_data = {
            "brand_id": brand_id,
            "brand_name": brand_name,
            "created_at": datetime.utcnow(),
            "status": "completed",
            "metrics": {
                "overall_score": overall_score,
                "platform_coverage": platform_coverage,
                "mention_frequency": mention_frequency,
                "sentiment_score": sentiment_score,
                "recommendation_likelihood": recommendation_likelihood,
                "cost": report_cost / 100,  # Convert to dollars
                "trend_vs_previous": trend_vs_previous
            },
            "platforms_analyzed": [
                "ChatGPT", "Claude", "Gemini", "Perplexity", "Bing Chat",
                "Character.AI", "Poe", "You.com", "Phind", "Kagi"
            ],
            "detailed_analysis": {
                "platform_breakdown": {
                    "ChatGPT": {"mentions": random.randint(5, 25), "sentiment": round(random.uniform(0.5, 0.9), 2)},
                    "Claude": {"mentions": random.randint(3, 20), "sentiment": round(random.uniform(0.6, 0.9), 2)},
                    "Gemini": {"mentions": random.randint(4, 22), "sentiment": round(random.uniform(0.5, 0.8), 2)},
                    "Perplexity": {"mentions": random.randint(2, 15), "sentiment": round(random.uniform(0.6, 0.9), 2)},
                    "Bing Chat": {"mentions": random.randint(3, 18), "sentiment": round(random.uniform(0.5, 0.8), 2)}
                },
                "key_insights": [
                    f"{brand_name} shows strong presence across major AI platforms",
                    f"Sentiment analysis indicates {sentiment_score*100:.0f}% positive mentions",
                    f"Recommendation likelihood is {recommendation_likelihood*100:.0f}% across platforms"
                ],
                "recommendations": [
                    "Optimize content for AI model training data",
                    "Increase brand mention frequency in relevant contexts",
                    "Improve sentiment through better customer experience"
                ]
            }
        }

        # Save report to Firestore
        report_ref = db.collection("ai_visibility_reports").document(report_id)
        report_ref.set(report_data)

        # Deduct cost from wallet
        wallet_ref.update({
            "available_balance": firestore.Increment(-report_cost)
        })

        # Add transaction record
        transaction_data = {
            "user_id": brand_id,
            "amount": -report_cost,
            "type": "debit",
            "category": "ai_visibility_report",
            "description": f"AI Visibility Report - {report_id[:8]}",
            "reference_id": report_id,
            "reference_type": "ai_visibility_report",
            "created_at": datetime.utcnow(),
            "status": "completed"
        }

        db.collection("transactions").add(transaction_data)

        return {
            "message": "AI visibility report generated successfully",
            "report_id": report_id,
            "cost": report_cost / 100,
            "status": "completed"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating AI visibility report: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate report")

@router.get("/reports/{report_id}/download")
async def download_ai_visibility_report(
    report_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Download AI visibility report as PDF."""
    try:
        db = get_db()

        # Get brand ID from user
        brand_id = current_user.get("uid")
        if not brand_id:
            raise HTTPException(status_code=400, detail="Brand ID not found")

        # Get report
        report_ref = db.collection("ai_visibility_reports").document(report_id)
        report_doc = report_ref.get()

        if not report_doc.exists:
            raise HTTPException(status_code=404, detail="Report not found")

        report_data = report_doc.to_dict()

        # Verify ownership
        if report_data.get("brand_id") != brand_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # In production, this would generate an actual PDF
        # For now, return a simple response indicating the download would start
        return {
            "message": "PDF download would start here",
            "report_id": report_id,
            "filename": f"ai_visibility_report_{report_id[:8]}.pdf"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading AI visibility report: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to download report")

# GEO Wizard Models
class BrandInfo(BaseModel):
    brand_name: str
    website_url: str
    description: Optional[str] = None
    competitor_urls: List[str] = []
    sitemap_url: Optional[str] = None
    robots_url: Optional[str] = None
    region: str = "United States"
    selected_models: List[str] = ["gpt-4o", "claude-4", "gemini-2.5", "perplexity-sonar"]

class GeneratedPrompt(BaseModel):
    id: str
    text: str
    category: Optional[str] = None
    isCustom: Optional[bool] = False

class GeneratePromptsRequest(BaseModel):
    brand_name: str
    website_url: str
    description: Optional[str] = None
    competitor_urls: List[str] = []
    region: str = "United States"
    num_prompts: int = 10

class GeneratePromptsResponse(BaseModel):
    prompts: List[GeneratedPrompt]

class RegeneratePromptRequest(BaseModel):
    brand_name: str
    website_url: str
    existing_prompts: List[str]
    prompt_id: str

class RegeneratePromptResponse(BaseModel):
    new_prompt: str

class StartAnalysisRequest(BaseModel):
    brand_info: BrandInfo
    prompts: List[Dict[str, str]]  # [{"text": "...", "category": "..."}]
    selected_models: List[str]

class StartAnalysisResponse(BaseModel):
    analysis_id: str
    message: str

class AnalysisProgress(BaseModel):
    stage: str
    overall_progress: float
    models: List[Dict[str, Any]]
    estimated_time_remaining: int

class AnalysisProgressResponse(BaseModel):
    progress: AnalysisProgress
    result: Optional[Dict[str, Any]] = None




# Get brand profile for auto-fill
@geo_router.get("/geo-brand-profile")
async def get_brand_profile_for_geo(
    current_user: dict = Depends(get_current_user)
):
    """Get brand profile information for auto-filling the GEO wizard."""
    logger.info(f"Auto-fill API called for user: {current_user.get('uid')}")
    try:
        db = get_db()

        # Get brand ID from user
        brand_id = current_user.get("uid")
        if not brand_id:
            logger.error("Brand ID not found in current_user")
            raise HTTPException(status_code=400, detail="Brand ID not found")

        logger.info(f"Fetching brand profile for brand_id: {brand_id}")

        # Get brand information
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            logger.error(f"Brand document not found for brand_id: {brand_id}")
            raise HTTPException(status_code=404, detail="Brand not found")

        brand_data = brand_doc.to_dict()
        logger.info(f"Brand data found: {brand_data.get('company_name', 'No company_name')}")

        # Return relevant fields for the wizard
        result = {
            "brand_name": brand_data.get("company_name", ""),
            "website_url": brand_data.get("website", ""),
            "description": brand_data.get("description", ""),
            "industry": brand_data.get("industry", ""),
            "headquarters": brand_data.get("headquarters", "")
        }
        logger.info(f"Returning auto-fill data: {result}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching brand profile for GEO: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch brand profile")

# GEO Wizard Endpoints
@geo_router.post("/geo-generate-prompts", response_model=GeneratePromptsResponse)
async def generate_prompts(
    request: GeneratePromptsRequest,
    current_user: dict = Depends(get_current_user)
):
    """Generate AI-style prompts based on brand information using GPT-4o."""
    try:
        logger.info(f"🎯 Generate prompts request from user: {current_user.get('uid', 'unknown')}")
        logger.info(f"📋 Request data: brand_name={request.brand_name}, website_url={request.website_url}")
        logger.info(f"🔢 Number of prompts requested: {request.num_prompts}")
        logger.info(f"🔑 OpenRouter API key configured: {'Yes' if OPENROUTER_API_KEY else 'No'}")

        brand_name = request.brand_name
        industry = infer_industry(request.website_url, request.description)

        logger.info(f"🏭 Inferred industry: {industry}")

        # Create a detailed prompt for Claude-4 to generate realistic search queries
        generation_prompt = f"""You are an expert at creating realistic search queries that people would use when looking for business solutions.

I need you to generate EXACTLY {request.num_prompts} diverse, realistic search queries that potential customers might use when researching "{brand_name}" or similar {industry} solutions.

Brand Information:
- Brand Name: {brand_name}
- Website: {request.website_url}
- Industry: {industry}
- Description: {request.description or "Not provided"}
- Region: {request.region}
- Competitors: {", ".join(request.competitor_urls) if request.competitor_urls else "Not provided"}

Create queries that cover these categories:
- Direct brand searches
- Comparison searches
- Problem-solving searches
- Alternative/competitor searches
- Review and evaluation searches
- Regional/location-based searches
- Feature-specific searches
- Pricing and value searches

Requirements:
- Make queries sound natural and realistic
- Vary the length and complexity
- Include both direct brand mentions and indirect discovery scenarios
- Mix informational, commercial, and transactional intent
- Consider different user personas (startups, enterprises, individuals)

You must return EXACTLY {request.num_prompts} queries in a JSON array. Here is the EXACT format:

[
  {{"text": "Ramp vs Brex comparison", "category": "comparison"}},
  {{"text": "best expense management software", "category": "recommendation"}},
  {{"text": "Ramp pricing plans", "category": "pricing"}},
  {{"text": "corporate credit card solutions", "category": "features"}},
  {{"text": "Ramp reviews and ratings", "category": "evaluation"}},
  {{"text": "expense tracking alternatives", "category": "competitive"}},
  {{"text": "Ramp for small business", "category": "enterprise"}},
  {{"text": "how to use Ramp", "category": "guidance"}},
  {{"text": "Ramp available in California", "category": "regional"}},
  {{"text": "business expense management value", "category": "value"}}
]

CRITICAL REQUIREMENTS:
- Return EXACTLY {request.num_prompts} objects in the array
- Start with [ and end with ]
- Each object must have "text" and "category" fields
- Use categories: comparison, competitive, regional, recommendation, evaluation, enterprise, pricing, guidance, features, value
- Return ONLY the JSON array, no other text
- Each query should be realistic (2-8 words)

Generate the JSON array with {request.num_prompts} queries now:"""

        # Call Claude-4 to generate prompts (with limited retries)
        logger.info("Calling Claude-4 for prompt generation (max 2 attempts)...")
        result = await call_openrouter_model(generation_prompt, "claude-4", max_retries=2)

        logger.info(f"GPT-4o result success: {result.get('success', False)}")

        if result["success"]:
            try:
                # Extract JSON from markdown code blocks if present
                content = result["content"].strip()
                logger.info(f"📄 Raw content: {content[:200]}...")

                # Check if content is wrapped in markdown code blocks
                if content.startswith("```json") and content.endswith("```"):
                    # Extract JSON from markdown
                    json_content = content[7:-3].strip()  # Remove ```json and ```
                    logger.info(f"🔧 Extracted JSON from markdown: {json_content[:200]}...")
                elif content.startswith("```") and content.endswith("```"):
                    # Generic code block
                    json_content = content[3:-3].strip()  # Remove ``` and ```
                    logger.info(f"🔧 Extracted content from code block: {json_content[:200]}...")
                else:
                    # No markdown wrapper
                    json_content = content
                    logger.info(f"📝 Using content as-is: {json_content[:200]}...")

                # Parse the JSON response from GPT-4o
                generated_data = json.loads(json_content)
                logger.info(f"✅ JSON parsing successful, got {len(generated_data)} items")
                logger.info(f"🔍 Generated data type: {type(generated_data)}")
                logger.info(f"🔍 Generated data: {generated_data}")

                prompts = []
                # Handle different response structures
                if isinstance(generated_data, list):
                    # Direct list of prompts
                    items_to_process = generated_data[:request.num_prompts]
                    logger.info(f"📋 Processing direct list with {len(items_to_process)} items")
                elif isinstance(generated_data, dict):
                    # Check for nested structures
                    if "queries" in generated_data and isinstance(generated_data["queries"], list):
                        # GPT-4o returned {"queries": [...]}
                        items_to_process = generated_data["queries"][:request.num_prompts]
                        logger.info(f"📋 Found 'queries' key with {len(items_to_process)} items")
                    elif "prompts" in generated_data and isinstance(generated_data["prompts"], list):
                        # Alternative structure {"prompts": [...]}
                        items_to_process = generated_data["prompts"][:request.num_prompts]
                        logger.info(f"📋 Found 'prompts' key with {len(items_to_process)} items")
                    elif "text" in generated_data:
                        # Single prompt object
                        items_to_process = [generated_data]
                        logger.info(f"📋 Processing single prompt object")
                    else:
                        logger.error(f"❌ Dict structure not recognized: {list(generated_data.keys())}")
                        raise ValueError(f"Unrecognized dict structure with keys: {list(generated_data.keys())}")
                else:
                    logger.error(f"❌ Unexpected data type: {type(generated_data)}")
                    raise ValueError(f"Expected list or dict, got {type(generated_data)}")

                logger.info(f"🔄 Processing {len(items_to_process)} items")
                for i, item in enumerate(items_to_process):
                    if isinstance(item, dict):
                        prompt = GeneratedPrompt(
                            id=str(uuid.uuid4()),
                            text=item.get("text", ""),
                            category=item.get("category", "general")
                        )
                        prompts.append(prompt)
                        logger.info(f"  {i+1}. [{prompt.category}] {prompt.text}")
                    else:
                        logger.warning(f"⚠️ Skipping non-dict item {i+1}: {item}")

                logger.info(f"✅ Successfully generated {len(prompts)} prompts using GPT-4o")
                return GeneratePromptsResponse(prompts=prompts)

            except json.JSONDecodeError as e:
                logger.error(f"❌ Failed to parse GPT-4o response as JSON: {str(e)}")
                logger.error(f"🔍 Content that failed to parse: '{result.get('content', 'NO CONTENT')}'")
                logger.warning("⚠️ Falling back to template-based generation")
                return generate_fallback_prompts(request, brand_name, industry)
        else:
            logger.error(f"GPT-4o call failed: {result.get('error', 'Unknown error')}")
            logger.warning("Falling back to template-based generation")
            return generate_fallback_prompts(request, brand_name, industry)

    except Exception as e:
        logger.error(f"❌ Error generating prompts: {str(e)}")
        logger.error(f"🔍 Exception type: {type(e).__name__}")
        import traceback
        logger.error(f"📋 Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to generate prompts: {str(e)}")


def generate_fallback_prompts(request: GeneratePromptsRequest, brand_name: str, industry: str) -> GeneratePromptsResponse:
    """Generate fallback prompts using templates when GPT-4o is unavailable."""
    logger.info("Using fallback template-based prompt generation")

    prompt_templates = [
        f"What are the best {industry} solutions for small businesses?",
        f"How does {brand_name} compare to other {industry} providers?",
        f"What are the top-rated {industry} tools in {request.region}?",
        f"I need a reliable {industry} solution for my startup. Any recommendations?",
        f"What are the pros and cons of {brand_name}?",
        f"Best {industry} platforms for enterprise use?",
        f"{brand_name} vs competitors: which is better for pricing?",
        f"How to choose the right {industry} provider?",
        f"What features should I look for in a {industry} solution?",
        f"Is {brand_name} worth the investment for {industry}?",
        f"What do users say about {brand_name}?",
        f"Alternatives to {brand_name} for {industry}?",
        f"Best {industry} software for startups?",
        f"{brand_name} review: is it good for {industry}?",
        f"Comparing {industry} tools: {brand_name} vs others"
    ]

    # Select and randomize prompts
    selected_prompts = random.sample(prompt_templates, min(request.num_prompts, len(prompt_templates)))

    prompts = []
    categories = ["comparison", "competitive", "regional", "recommendation", "evaluation",
                 "enterprise", "pricing", "guidance", "features", "value"]

    for i, prompt_text in enumerate(selected_prompts):
        prompts.append(GeneratedPrompt(
            id=str(uuid.uuid4()),
            text=prompt_text,
            category=categories[i % len(categories)]
        ))

    return GeneratePromptsResponse(prompts=prompts)


@geo_router.post("/geo-regenerate-prompt", response_model=RegeneratePromptResponse)
async def regenerate_prompt(
    request: RegeneratePromptRequest,
    current_user: dict = Depends(get_current_user)
):
    """Regenerate a single prompt."""
    try:
        # Generate alternative prompts
        alternatives = [
            f"What are the best alternatives to {request.brand_name}?",
            f"How does {request.brand_name} compare in terms of features?",
            f"Is {request.brand_name} suitable for enterprise use?",
            f"What do users say about {request.brand_name}?",
            f"{request.brand_name} pricing vs competitors?",
            f"Pros and cons of using {request.brand_name}?",
            f"Best practices for implementing {request.brand_name}?",
            f"How reliable is {request.brand_name} for business use?",
            f"What makes {request.brand_name} different from competitors?",
            f"Should I choose {request.brand_name} for my business?"
        ]

        # Filter out existing prompts
        available_alternatives = [alt for alt in alternatives if alt not in request.existing_prompts]

        if not available_alternatives:
            available_alternatives = alternatives  # Fallback to all if none available

        new_prompt = random.choice(available_alternatives)

        return RegeneratePromptResponse(new_prompt=new_prompt)

    except Exception as e:
        logger.error(f"Error regenerating prompt: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to regenerate prompt")








def infer_industry(website_url: str, description: Optional[str] = None) -> str:
    """Infer industry from website URL and description."""
    text = f"{website_url} {description or ''}".lower()

    if any(term in text for term in ["payment", "fintech", "finance", "banking"]):
        return "payment processing"
    elif any(term in text for term in ["ecommerce", "shop", "retail", "store"]):
        return "ecommerce"
    elif any(term in text for term in ["software", "saas", "app", "platform"]):
        return "software"
    elif any(term in text for term in ["marketing", "advertising", "ads"]):
        return "marketing"
    elif any(term in text for term in ["analytics", "data", "insights"]):
        return "analytics"
    elif any(term in text for term in ["health", "medical", "healthcare"]):
        return "healthcare"
    elif any(term in text for term in ["education", "learning", "training"]):
        return "education"

    return "business"


async def run_ai_visibility_analysis(analysis_id: str, analysis_data: Dict[str, Any]) -> None:
    """Run the complete AI visibility analysis pipeline"""
    try:
        db = get_db()
        analysis_ref = db.collection("geo_analyses").document(analysis_id)

        brand_info = analysis_data.get("brand_info", {})
        prompts = analysis_data.get("prompts", [])
        selected_models = analysis_data.get("selected_models", [])
        brand_name = brand_info.get("brand_name", "")
        competitors = brand_info.get("competitor_urls", [])

        logger.info(f"Starting AI visibility analysis for {brand_name} with {len(prompts)} prompts and {len(selected_models)} models")

        # Update status to running
        analysis_ref.update({
            "status": "running",
            "progress.stage": "model_execution",
            "progress.overall_progress": 10
        })

        # Step 1: Execute all model-prompt combinations
        model_responses = {}
        total_combinations = len(prompts) * len(selected_models)
        completed_combinations = 0

        # Create all model-prompt combinations for parallel processing
        tasks = []
        for model in selected_models:
            model_responses[model] = []
            for prompt_data in prompts:
                prompt_text = prompt_data.get("text", "")

                # Create the production-ready prompt with structured evaluation
                full_prompt = create_production_ai_visibility_prompt(
                    brand_name=brand_name,
                    brand_description=brand_info.get("description", ""),
                    analysis_prompt=prompt_text
                )

                # Create task for parallel execution
                task = {
                    "model": model,
                    "prompt_data": prompt_data,
                    "full_prompt": full_prompt,
                    "original_prompt": prompt_text
                }
                tasks.append(task)

        # Execute all tasks in parallel with controlled concurrency
        semaphore = asyncio.Semaphore(5)  # Limit to 5 concurrent requests

        async def process_single_task(task):
            async with semaphore:
                model = task["model"]
                prompt_data = task["prompt_data"]
                full_prompt = task["full_prompt"]
                original_prompt = task["original_prompt"]

                # Call the model with the production prompt
                response = await call_openrouter_model(full_prompt, model)

                if response["success"]:
                    # Parse the structured response
                    parsed_response = parse_ai_visibility_response(response["content"])

                    return {
                        "model": model,
                        "prompt": original_prompt,
                        "prompt_category": prompt_data.get("category", ""),
                        "natural_answer": parsed_response.get("natural_answer", ""),
                        "raw_response": response["content"],
                        "evaluation": parsed_response.get("evaluation", {}),
                        "model_usage": response.get("usage", {}),
                        "success": parsed_response.get("success", False),
                        "error": parsed_response.get("error") if not parsed_response.get("success", False) else None
                    }
                else:
                    return {
                        "model": model,
                        "prompt": original_prompt,
                        "prompt_category": prompt_data.get("category", ""),
                        "natural_answer": "",
                        "raw_response": "",
                        "evaluation": {
                            "mentioned": False,
                            "rank": None,
                            "sentiment": "neutral",
                            "strength": "weak"
                        },
                        "model_usage": {},
                        "success": False,
                        "error": response["error"]
                    }

        # Execute all tasks in parallel
        logger.info(f"Executing {len(tasks)} model-prompt combinations in parallel")
        results = await asyncio.gather(*[process_single_task(task) for task in tasks], return_exceptions=True)

        # Organize results by model
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Task failed with exception: {result}")
                continue

            model = result["model"]
            if model not in model_responses:
                model_responses[model] = []
            model_responses[model].append(result)

            completed_combinations += 1
            progress = 10 + (completed_combinations / total_combinations) * 70  # 10-80% for model execution

            # Update progress
            analysis_ref.update({
                "progress.overall_progress": progress,
                f"progress.models": firestore.ArrayUnion([{
                    "model": model,
                    "status": "processing",
                    "progress": (len(model_responses[model]) / len(prompts)) * 100,
                    "prompts_completed": len(model_responses[model]),
                    "total_prompts": len(prompts)
                }])
            })

        # Step 3: Aggregate results per model
        analysis_ref.update({
            "progress.stage": "aggregating_results",
            "progress.overall_progress": 80
        })

        model_results = {}
        for model, responses in model_responses.items():
            model_results[model] = aggregate_model_results(model, responses, brand_name)

        # Step 4: Generate final report
        analysis_ref.update({
            "progress.stage": "generating_report",
            "progress.overall_progress": 90
        })

        final_report = generate_final_report(model_results, brand_info, prompts)

        # Step 5: Complete analysis
        analysis_ref.update({
            "status": "completed",
            "progress.stage": "completed",
            "progress.overall_progress": 100,
            "result": final_report,
            "model_responses": model_responses,
            "completed_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Completed AI visibility analysis for {brand_name}")

    except Exception as e:
        logger.error(f"Error in AI visibility analysis: {str(e)}")
        # Update status to failed
        db.collection("geo_analyses").document(analysis_id).update({
            "status": "failed",
            "error": str(e),
            "failed_at": firestore.SERVER_TIMESTAMP
        })

def aggregate_model_results(model: str, responses: list, brand_name: str) -> dict:
    """Aggregate results for a single model across all prompts"""
    total_prompts = len(responses)
    successful_responses = [r for r in responses if r["success"]]

    if not successful_responses:
        return {
            "model": model,
            "visibility_score": 0,
            "mentions": 0,
            "positive_mentions": 0,
            "top_ranking_count": 0,
            "total_prompts": total_prompts,
            "successful_responses": 0,
            "improvement_areas": ["Model failed to respond to prompts"],
            "prompt_evaluations": responses
        }

    # Calculate metrics using the new structured evaluation format
    mentions = sum(1 for r in successful_responses if r["evaluation"].get("mentioned", False))
    positive_mentions = sum(1 for r in successful_responses
                          if r["evaluation"].get("mentioned", False) and r["evaluation"].get("sentiment") == "positive")
    strong_recommendations = sum(1 for r in successful_responses
                               if r["evaluation"].get("mentioned", False) and r["evaluation"].get("strength") == "strong")
    top_ranking_count = sum(1 for r in successful_responses
                           if r["evaluation"].get("rank") is not None and r["evaluation"].get("rank") <= 2)

    # Calculate visibility score (0-100) with updated metrics
    mention_rate = mentions / len(successful_responses)
    positive_rate = positive_mentions / max(mentions, 1)
    strong_rate = strong_recommendations / max(mentions, 1)
    top_ranking_rate = top_ranking_count / len(successful_responses)

    # Updated scoring formula to include strength
    visibility_score = int((mention_rate * 0.3 + positive_rate * 0.25 + strong_rate * 0.25 + top_ranking_rate * 0.2) * 100)

    # Collect improvement suggestions based on evaluation patterns
    improvement_areas = []
    if mention_rate < 0.5:
        improvement_areas.append("Increase brand visibility in AI recommendations")
    if positive_rate < 0.7:
        improvement_areas.append("Improve brand sentiment and positioning")
    if strong_rate < 0.5:
        improvement_areas.append("Strengthen competitive positioning")
    if top_ranking_rate < 0.3:
        improvement_areas.append("Enhance ranking in recommendation lists")

    return {
        "model": model,
        "visibility_score": visibility_score,
        "mentions": mentions,
        "positive_mentions": positive_mentions,
        "strong_recommendations": strong_recommendations,
        "top_ranking_count": top_ranking_count,
        "total_prompts": total_prompts,
        "successful_responses": len(successful_responses),
        "mention_rate": round(mention_rate * 100, 1),
        "positive_rate": round(positive_rate * 100, 1),
        "top_ranking_rate": round(top_ranking_rate * 100, 1),
        "improvement_areas": improvement_areas[:5],  # Top 5 suggestions
        "prompt_evaluations": responses
    }

def generate_final_report(model_results: dict, brand_info: dict, prompts: list) -> dict:
    """Generate the final aggregated report across all models"""
    brand_name = brand_info.get("brand_name", "")

    # Calculate overall metrics
    total_models = len(model_results)
    if total_models == 0:
        return {"error": "No model results available"}

    overall_score = sum(result["visibility_score"] for result in model_results.values()) // total_models
    total_mentions = sum(result["mentions"] for result in model_results.values())
    total_positive = sum(result["positive_mentions"] for result in model_results.values())
    total_top_rankings = sum(result["top_ranking_count"] for result in model_results.values())

    # Find best and worst performing models
    best_model = max(model_results.items(), key=lambda x: x[1]["visibility_score"])
    worst_model = min(model_results.items(), key=lambda x: x[1]["visibility_score"])

    # Collect all improvement areas
    all_improvements = []
    for result in model_results.values():
        all_improvements.extend(result["improvement_areas"])

    # Get unique improvements and count frequency
    improvement_frequency = {}
    for improvement in all_improvements:
        improvement_frequency[improvement] = improvement_frequency.get(improvement, 0) + 1

    top_improvements = sorted(improvement_frequency.items(), key=lambda x: x[1], reverse=True)[:5]

    return {
        "report_id": str(uuid.uuid4()),
        "brand_name": brand_name,
        "analysis_date": datetime.utcnow().isoformat(),
        "overall_score": overall_score,
        "total_prompts_tested": len(prompts),
        "total_models_tested": total_models,
        "summary": {
            "total_mentions": total_mentions,
            "positive_mentions": total_positive,
            "top_rankings": total_top_rankings,
            "mention_rate": round((total_mentions / (len(prompts) * total_models)) * 100, 1),
            "positive_rate": round((total_positive / max(total_mentions, 1)) * 100, 1)
        },
        "model_performance": {
            "best_model": {
                "name": best_model[0],
                "score": best_model[1]["visibility_score"]
            },
            "worst_model": {
                "name": worst_model[0],
                "score": worst_model[1]["visibility_score"]
            },
            "detailed_results": model_results
        },
        "insights": {
            "top_improvement_areas": [item[0] for item in top_improvements],
            "consistency_across_models": "high" if (best_model[1]["visibility_score"] - worst_model[1]["visibility_score"]) < 20 else "low",
            "recommendation": generate_recommendation(overall_score, model_results)
        },
        "cost_analysis": calculate_analysis_cost(model_results)
    }

def generate_recommendation(overall_score: int, model_results: dict) -> str:
    """Generate actionable recommendations based on the analysis"""
    if overall_score >= 80:
        return f"Excellent AI visibility! Your brand is consistently well-represented across AI models. Focus on maintaining this strong presence."
    elif overall_score >= 60:
        return f"Good AI visibility with room for improvement. Consider optimizing content for better positioning in AI responses."
    elif overall_score >= 40:
        return f"Moderate AI visibility. Significant opportunities exist to improve brand positioning and content optimization."
    else:
        return f"Low AI visibility detected. Immediate action needed to improve brand presence in AI model responses."

def calculate_analysis_cost(model_results: dict) -> dict:
    """Calculate the cost of the analysis based on token usage"""
    total_cost = 0
    cost_breakdown = {}

    for model, results in model_results.items():
        model_cost = 0
        total_tokens = 0

        for response in results["prompt_evaluations"]:
            if response["success"]:
                # Model usage tokens
                model_tokens = response["model_usage"].get("total_tokens", 0)
                # Evaluator usage tokens (GPT-4o)
                eval_tokens = response["evaluator_usage"].get("total_tokens", 0)

                total_tokens += model_tokens + eval_tokens

                # Calculate cost (convert from per 1000 tokens to actual cost)
                model_cost += (model_tokens * MODEL_COSTS.get(model, 5)) / 1000
                model_cost += (eval_tokens * MODEL_COSTS.get("gpt-4o", 5)) / 1000  # Evaluator cost

        cost_breakdown[model] = {
            "cost_cents": round(model_cost, 2),
            "total_tokens": total_tokens
        }
        total_cost += model_cost

    return {
        "total_cost_cents": round(total_cost, 2),
        "total_cost_dollars": round(total_cost / 100, 4),
        "cost_breakdown": cost_breakdown
    }

def generate_mock_analysis_result(analysis_data: Dict[str, Any]) -> Dict[str, Any]:
    """Generate mock analysis results."""
    brand_info = analysis_data.get("brand_info", {})
    selected_models = analysis_data.get("selected_models", [])
    prompts = analysis_data.get("prompts", [])

    # Generate overall score
    overall_score = random.randint(70, 95)

    # Generate model-specific results
    model_results = {}
    for model in selected_models:
        model_results[model] = {
            "visibility_score": random.randint(60, 100),
            "mentions": random.randint(0, len(prompts)),
            "positive_mentions": random.randint(0, len(prompts)),
            "top_ranking_count": random.randint(0, len(prompts) // 2)
        }

    return {
        "overall_score": overall_score,
        "model_results": model_results,
        "cost": analysis_data.get("total_cost", 0) / 100,  # Convert to dollars
        "insights": [
            f"Brand visibility varies across AI models",
            f"Strongest performance on {random.choice(selected_models)}",
            f"Analyzed {len(prompts)} prompts across {len(selected_models)} models"
        ]
    }

# New GEO Analysis Models
class GeoAnalysisRequest(BaseModel):
    brand_info: BrandInfo
    prompts: List[GeneratedPrompt]
    selected_models: List[str]

@geo_router.post("/geo-start-analysis")
async def start_geo_analysis(
    request: GeoAnalysisRequest,
    current_user: dict = Depends(get_current_user)
):
    """Start a new GEO analysis with real AI model execution"""
    try:
        db = get_db()

        # Validate OpenRouter API key
        if not OPENROUTER_API_KEY:
            raise HTTPException(status_code=500, detail="OpenRouter API key not configured")

        # Create analysis document
        analysis_id = str(uuid.uuid4())
        analysis_data = {
            "id": analysis_id,
            "user_id": current_user["uid"],
            "brand_info": request.brand_info.dict(),
            "prompts": [prompt.dict() for prompt in request.prompts],
            "selected_models": request.selected_models,
            "status": "processing",
            "created_at": firestore.SERVER_TIMESTAMP,
            "progress": {
                "stage": "initializing",
                "overall_progress": 0,
                "models": []
            }
        }

        # Save to Firestore
        analysis_ref = db.collection("geo_analyses").document(analysis_id)
        analysis_ref.set(analysis_data)

        logger.info(f"Started GEO analysis {analysis_id} for user {current_user['uid']} with models: {request.selected_models}")

        # Start the analysis in the background
        asyncio.create_task(run_ai_visibility_analysis(analysis_id, analysis_data))

        return {
            "analysis_id": analysis_id,
            "status": "processing",
            "message": "Analysis started successfully",
            "estimated_duration_minutes": len(request.prompts) * len(request.selected_models) * 0.5,  # Rough estimate
            "total_model_calls": len(request.prompts) * len(request.selected_models)
        }

    except Exception as e:
        logger.error(f"Error starting GEO analysis: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to start analysis")

@geo_router.get("/geo-analysis/{analysis_id}")
async def get_geo_analysis_result(
    analysis_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get the results of a completed GEO analysis"""
    try:
        db = get_db()

        # Get analysis document
        analysis_ref = db.collection("geo_analyses").document(analysis_id)
        analysis_doc = analysis_ref.get()

        if not analysis_doc.exists:
            raise HTTPException(status_code=404, detail="Analysis not found")

        analysis_data = analysis_doc.to_dict()

        # Verify ownership
        if analysis_data.get("user_id") != current_user["uid"]:
            raise HTTPException(status_code=403, detail="Access denied")

        return {
            "analysis_id": analysis_id,
            "status": analysis_data.get("status"),
            "progress": analysis_data.get("progress", {}),
            "result": analysis_data.get("result"),
            "created_at": analysis_data.get("created_at"),
            "completed_at": analysis_data.get("completed_at"),
            "error": analysis_data.get("error")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting GEO analysis result: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get analysis result")

@router.get("/analysis/{analysis_id}/status")
async def get_analysis_status(
    analysis_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get the status of an AI Visibility analysis (for polling)"""
    try:
        db = get_db()

        # Get analysis document from geo_analyses collection
        analysis_ref = db.collection("geo_analyses").document(analysis_id)
        analysis_doc = analysis_ref.get()

        if not analysis_doc.exists:
            raise HTTPException(status_code=404, detail="Analysis not found")

        analysis_data = analysis_doc.to_dict()

        # Verify ownership
        if analysis_data.get("user_id") != current_user["uid"]:
            raise HTTPException(status_code=403, detail="Access denied")

        status = analysis_data.get("status", "unknown")
        progress = analysis_data.get("progress", {})

        response = {
            "analysis_id": analysis_id,
            "status": status,
            "progress": progress,
            "created_at": analysis_data.get("created_at"),
            "estimated_completion": None
        }

        # Add completion info if available
        if status == "completed":
            response["completed_at"] = analysis_data.get("completed_at")
            response["result_available"] = True
        elif status == "failed":
            response["error"] = analysis_data.get("error")
        elif status in ["processing", "running"]:
            # Estimate completion time based on progress
            overall_progress = progress.get("overall_progress", 0)
            if overall_progress > 0:
                # Rough estimate: if we're X% done, we have (100-X)/X times the elapsed time left
                elapsed_minutes = 2  # Rough estimate of elapsed time
                remaining_minutes = max(1, int((100 - overall_progress) / max(overall_progress, 1) * elapsed_minutes))
                response["estimated_minutes_remaining"] = remaining_minutes

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get analysis status")

@router.get("/analysis/{analysis_id}/status")
async def get_analysis_status(
    analysis_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get the status of an AI Visibility analysis (for polling)"""
    try:
        db = get_db()

        # Get analysis document from geo_analyses collection
        analysis_ref = db.collection("geo_analyses").document(analysis_id)
        analysis_doc = analysis_ref.get()

        if not analysis_doc.exists:
            raise HTTPException(status_code=404, detail="Analysis not found")

        analysis_data = analysis_doc.to_dict()

        # Verify ownership
        if analysis_data.get("user_id") != current_user["uid"]:
            raise HTTPException(status_code=403, detail="Access denied")

        status = analysis_data.get("status", "unknown")
        progress = analysis_data.get("progress", {})

        response = {
            "analysis_id": analysis_id,
            "status": status,
            "progress": progress,
            "created_at": analysis_data.get("created_at"),
            "estimated_completion": None
        }

        # Add completion info if available
        if status == "completed":
            response["completed_at"] = analysis_data.get("completed_at")
            response["result_available"] = True
        elif status == "failed":
            response["error"] = analysis_data.get("error")
        elif status in ["processing", "running"]:
            # Estimate completion time based on progress
            overall_progress = progress.get("overall_progress", 0)
            if overall_progress > 0:
                # Rough estimate: if we're X% done, we have (100-X)/X times the elapsed time left
                elapsed_minutes = 2  # Rough estimate of elapsed time
                remaining_minutes = max(1, int((100 - overall_progress) / max(overall_progress, 1) * elapsed_minutes))
                response["estimated_minutes_remaining"] = remaining_minutes

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get analysis status")
